/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  Dimensions,
  Modal,
  // Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {translate} from '../../lang/Translate';
import Entypo from 'react-native-vector-icons/Entypo';
import PropTypes from 'prop-types';
import {store} from '../../redux/store/configureStore';
import {isObject} from '@app/utils/lodashFactions';
import styles from './styles';
import {Images} from '../../config/images';
import {BaseColors} from '@config/theme';
import {CustomIcon} from '@config/LoadIcons';
import Button from '@components/UI/Button';
const IOS = Platform.OS === 'ios';

/**
 * Module NewGroup for creating new group
 * @module AlertModal
 *
 */

export default function AlertModal(props: {
  lottieSrc?: any;
  lottieStyle?: any;
  image?: any;
  title?: string;
  description?: string;
  btnYTitle?: string;
  btnNTitle?: string;
  btnYPress?: any;
  onPressCancle?: any;
  btnNPress?: any;
  visible?: boolean;
  setVisible?: any;
  loader?: boolean;
  nLoader?: boolean;
  prompt?: boolean;
  btnOkPress?: any;
  outsidePressed?: any;
  lottieViewVisible?: boolean;
  link?: boolean;
  type?: string;
  onPressLink?: any;
  onPressLink2?: any;
  link1?: string;
  subTitleTxt?: string;
  link2?: string;
  connector?: any;
  horizontalButton?: boolean;
  customClose?: boolean;
  subTitle?: boolean;
  disable?: boolean;
  noButton?: boolean;
  blurBackground?: any;
  imgStyle?: any;
  borderStyle?: any;
  btnNstyle?: any;
  titlesty?: any;
  viewStyles?: any;
  reRenderIssue?: any;
  confirmation?: any;
  okbtnPromp?: any;
  completeProfile?: any;
}) {
  const {
    lottieSrc = Images.alert,
    lottieStyle,
    image,
    confirmation,
    title,
    description,
    btnYTitle,
    type,
    subTitle,
    subTitleTxt,
    btnNTitle,
    btnYPress,
    onPressCancle,
    btnNPress,
    outsidePressed,
    visible,
    setVisible,
    nLoader,
    loader,
    prompt,
    btnOkPress,
    lottieViewVisible,
    link,
    onPressLink,
    onPressLink2,
    link1,
    link2,
    noButton,
    connector,
    customClose,
    horizontalButton,
    disable,
    blurBackground,
    imgStyle,
    borderStyle,
    btnNstyle,
    titlesty,
    viewStyles,
    reRenderIssue = false,
    okbtnPromp,
    completeProfile,
  } = props;

  const {
    auth: {isTablet, darkmode},
  } = store.getState();
  const [autoPlayLottie, setAutoPlayLottie] = useState(true);
  useEffect(() => {
    if (reRenderIssue && !IOS) {
      setTimeout(() => {
        setAutoPlayLottie(false);
      }, 200);
    }
  }, []);
  return (
    <TouchableOpacity activeOpacity={1} testID="alert_modal_container">
      <Modal
        testID="alert_modal"
        animationType="slide"
        transparent={true}
        visible={visible}
        onRequestClose={() => {
          customClose ? btnNPress() : null;
          isObject(visible) ? setVisible({}) : setVisible(!visible);
          noButton ? outsidePressed() : null;
        }}>
        <TouchableOpacity
          testID="alert_modal_visible"
          activeOpacity={1}
          // onPress={() => {
          //   customClose ? btnNPress() : null;
          //   !loader
          //     ? isObject(visible)
          //       ? setVisible({})
          //       : setVisible(!visible)
          //     : null;
          //   noButton ? outsidePressed() : null;
          // }}
          style={[styles.mainView]}>
          <View
            testID="alert_modal_view"
            style={{
              ...styles.modalView,
              marginHorizontal: isTablet
                ? Dimensions.get('window').width / 4
                : IOS
                ? 20
                : 30,
              ...viewStyles,
              backgroundColor: BaseColors.white,
              borderColor: 'transparent',
              elevation: 3,
            }}>
            {image ? (
              okbtnPromp ? (
                <Text
                  testID="lottie_text"
                  style={[
                    styles.imageTitle,
                    {
                      textAlign: 'center',
                      color: BaseColors.textBlack,

                      marginBottom: 15,
                    },
                  ]}>
                  {title}
                </Text>
              ) : (
                <View
                  style={styles.imgViewStyle}
                  testID="alert_modal_image_contianer">
                  {confirmation ? null : (
                    <CustomIcon
                      name="verified"
                      testID="alert_modal_image"
                      color={'#2568ef'}
                      size={32}
                    />
                  )}

                  <Text
                    testID="lottie_text"
                    style={[
                      styles.imageTitle,
                      {alignContent: 'center', color: BaseColors.textBlack},
                      titlesty,
                    ]}>
                    {title}
                  </Text>
                </View>
              )
            ) : null}
            {link ? (
              <View testID="main_view">
                <Text
                  style={styles.linkDescriptionStyle}
                  testID="link_description_text">
                  {description}
                </Text>
                <View style={styles.linkViewStyle} testID="link_view">
                  <Text
                    testID="link_text"
                    style={{...styles.linkStyle, color: BaseColors.text}}
                    onPress={onPressLink}>
                    {link1}
                  </Text>
                  <Text
                    testID="connector_text"
                    style={{
                      ...styles.linkConnectorStyle,
                      color: BaseColors.text,
                    }}>
                    {connector}
                  </Text>
                  <Text
                    testID="link2_text"
                    style={{
                      ...styles.secondLinkStyle,
                      color: BaseColors.text,
                    }}
                    onPress={onPressLink2}>
                    {link2}
                  </Text>
                </View>
              </View>
            ) : lottieViewVisible ? (
              <View testID="sub_title_view">
                <Text style={{...styles.modalText, color: BaseColors.text}}>
                  {description}
                  {subTitle && (
                    <Text testID="sub_title_text" style={styles.subtitleTxt}>
                      {subTitleTxt}
                    </Text>
                  )}
                </Text>
              </View>
            ) : (
              //Delete your Account details
              <View style={styles.DesView} testID="des_view">
                <View style={styles.rowViewStyle} testID="row_view">
                  <Entypo
                    style={styles.listicon}
                    name="dot-single"
                    testID="dot_single_icon"
                  />
                  <Text style={styles.deleteAcText} testID="delete_text">
                    {translate('DeleteAcount', '')}
                  </Text>
                </View>
                <View style={styles.rowViewStyle} testID="if_del_view">
                  <Entypo
                    style={styles.listicon}
                    name="dot-single"
                    testID="list_icon"
                  />
                  <Text style={styles.deleteAcText} testID="if_delete_text">
                    {'IfDeleteAccount'}
                  </Text>
                </View>
              </View>
            )}
            <View style={styles.btnView}>
              {completeProfile ? (
                <Button
                  type="text"
                  disable={disable}
                  loading={loader}
                  style={
                    {
                      // marginLeft: 10,
                    }
                  }
                  containerStyle={{
                    minWidth: '100%',
                  }}
                  onPress={btnYPress}>
                  <Text testID="btn_text" style={{...styles.textStyle}}>
                    {btnYTitle}
                  </Text>
                </Button>
              ) : okbtnPromp ? (
                <Button
                  style={{width: '100%'}}
                  type="text"
                  onPress={btnOkPress}>
                  {translate('Ok', '')}
                </Button>
              ) : (
                <>
                  {noButton ? null : prompt ? (
                    <Button
                      style={{width: '100%'}}
                      type="text"
                      onPress={btnOkPress}>
                      {translate('Continue', '')}
                    </Button>
                  ) : (
                    <View
                      testID="btn_view"
                      style={{
                        flexDirection: 'row',
                        flex: 1,
                        justifyContent: 'space-between',
                      }}>
                      <Button
                        loading={nLoader}
                        type={'outlined'}
                        borderStyle={borderStyle}
                        containerStyle={{
                          minWidth: '45%',
                        }}
                        disable={loader}
                        style={{
                          marginTop: horizontalButton ? 20 : 0,
                        }}
                        onPress={() => {
                          loader || nLoader ? null : btnNPress();
                        }}>
                        <Text
                          testID="btn_title_text"
                          style={[
                            {
                              color: horizontalButton
                                ? '#fff'
                                : BaseColors.primary,
                            },
                            btnNstyle,
                            styles.textStyle,
                          ]}>
                          {btnNTitle}
                        </Text>
                      </Button>
                      <Button
                        type="text"
                        disable={disable}
                        loading={loader}
                        style={
                          {
                            // marginLeft: 10,
                          }
                        }
                        containerStyle={{
                          minWidth: '45%',
                        }}
                        buttonTextStyle={{}}
                        onPress={btnYPress}>
                        <Text testID="btn_text" style={{...styles.textStyle}}>
                          {btnYTitle}
                        </Text>
                      </Button>

                      {horizontalButton && !noButton && (
                        <Button
                          type={'outlined'}
                          containerStyle={{minWidth: '50%'}}
                          style={{
                            marginTop: horizontalButton ? 20 : 0,
                          }}
                          onPress={onPressCancle}>
                          <Text
                            testID="cancel_btn_text"
                            style={[
                              styles.textStyle,
                              {color: BaseColors.black},
                            ]}>
                            {'cancel'}
                          </Text>
                        </Button>
                      )}
                    </View>
                  )}
                </>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </TouchableOpacity>
  );
}

AlertModal.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  btnYTitle: PropTypes.string,
  btnNTitle: PropTypes.string,
  btnYPress: PropTypes.func,
  onPressCancle: PropTypes.func,
  btnNPress: PropTypes.func,
  btnXPress: PropTypes.func,
  btnOkPress: PropTypes.func,
  bt: PropTypes.func,
  type: PropTypes.string,
  lottieViewVisible: PropTypes.bool,
  horizontalButton: PropTypes.bool,
  customClose: PropTypes.bool,
  nLoader: PropTypes.bool,
};

AlertModal.defaultProps = {
  title: 'Alert',
  description: '',
  btnYTitle: 'OK',
  btnNTitle: 'CANCEL',
  type: '',
  btnYPress: () => {},
  btnNPress: () => {},
  onPressCancle: () => {},
  onClose: false,
  loader: false,
  lottieViewVisible: true,
  btnOkPress: () => {},
  horizontalButton: false,
  customClose: false,
  nLoader: false,
};
