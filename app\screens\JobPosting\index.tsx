import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Keyboard,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import DocumentPicker from 'react-native-document-picker';
import {useDispatch, useSelector} from 'react-redux';
import authActions from '@redux/reducers/auth/actions';
import Header from '@components/Header';
import {translate} from '@language/Translate';
import TextInput from '@components/UI/TextInput';
import Button from '@components/UI/Button';
import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import {BaseColors} from '@config/theme';
import Placeautocomplete from '@components/Placeautocomplete';
import socketAction from '@redux/reducers/socket/actions';
import {getApiData} from '@app/utils/apiHelper';
import {
  calculateDuration,
  // calculateTotalSalary,
  formatDate,
  formatUSD,
  getFormattedAddress,
  getSettings,
  updateReview,
} from '@app/utils/CommonFunction';
import moment from 'moment';
import {camelCase, isEmpty, trim} from '@app/utils/lodashFactions';
import {CustomIcon} from '@config/LoadIcons';
import FastImage from 'react-native-fast-image';
import SubTagComponant from '@components/SubTagComponat';
import AiComponant from '@components/AiComponant';
import ImageCropPicker from 'react-native-image-crop-picker';
import AnimatedView from '@components/AnimatedView';
import DropdownList from '@components/DropDownList';
import SwitchComponent from '@components/SwitchComponant';
import RBSheet from 'react-native-raw-bottom-sheet';
import ActionSheet from 'react-native-actionsheet';
import Icon from 'react-native-vector-icons/Fontisto';
import EIcon from 'react-native-vector-icons/Entypo';
import FIcon from 'react-native-vector-icons/FontAwesome';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {smartAlbums} from '@config/staticdata';

const {emit, onReceive} = socketAction;

export interface SparkDataItem {
  id: number;
  name: string;
  key: string;
}

interface DropdownItem {
  title: string;
}

export const sparkData: SparkDataItem[] = [
  {id: 1, name: 'Easier Habit Building', key: 'Easier_Habit_building.mp4'},
  {id: 2, name: 'Why get a Journal?', key: 'Why_Get_A_Journal.mp4'},
];
const durationOptions: DropdownItem[] = [
  {title: translate('Per hour', '')},
  {title: translate('Per day', '')},
  {title: translate('Per week', '')},
  {title: translate('Per month', '')},
  {title: translate('Per year', '')},
];
// const currencyOptions: DropdownItem[] = [
//   {title: translate('USD', '')},
//   {title: translate('EURO', '')},
// ];

// Define the ErrorState type
interface ErrorState {
  err: boolean;
  txt: string;
}

export default function JobPosting({navigation, route}: any) {
  const [skillsOptions, setSkillsOptions] = useState({loader: false, data: []});
  const {params} = route;
  const dispatch = useDispatch();
  const isCustomHarbor = params?.type === 'custom';
  const refRBSheet = useRef<any>(null);
  const {userData} = useSelector((s: any) => s.auth);
  const [loader, setLoader] = useState<boolean>(false);
  const [postLoader, setPostLoader] = useState<boolean>(false);
  const locationRef = useRef<any>(null);
  const jobID = route?.params?.jobID;
  const getList = route?.params?.getList;
  const [jobName, setJobName] = useState<any>(jobID ? jobID?.title : '');
  const [checkBoxErr, setCheckBoxErr] = useState(false);
  const [checkBoxErrTxt, setCheckBoxErrTxt] = useState('');
  const edit = route?.params?.edit;
  const [formatedAddress, setFormatedAddresss] = useState(
    jobID?.shortAddress ? jobID?.shortAddress : '',
  );

  const [imageLoader, setImageLoader] = useState<boolean>(false);

  const [jobNameErr, setJobNameErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [locationErr, setLocationErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });

  const [selectedtagErr, setSelectedTagErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [selectDateErrTxt, setSelectDateErrTxt] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [endDateErr, setEndDateErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [endTimeErr, setEndTimeErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [startTimeErr, setStartTimeErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [durationErr, setDurationErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [salaryAmountErr, setSalaryAmountErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });

  const [about, setAbout] = useState<any>('');
  const [aiDescription, setAiDescription] = useState<any>(
    jobID?.description ? jobID?.description : '',
  );
  const IOS = Platform.OS === 'ios';

  const [startDate, setStartDate] = useState<Date | undefined>(
    jobID?.startDate ? moment(jobID?.startDate, 'MM/DD/YYYY') : undefined,
  );

  // Pre-fill states with jobID data
  const [endDate, setEndDate] = useState<Date | undefined>(
    jobID?.endDate ? moment(jobID?.endDate, 'MM/DD/YYYY') : undefined,
  );
  const [startTime, setStartTime] = useState<Date | undefined>(
    jobID?.startTime ? moment(jobID.startTime, 'hh:mm A').toDate() : undefined,
  );
  const [endTime, setEndTime] = useState<Date | undefined>(
    jobID?.endTime ? moment(jobID.endTime, 'hh:mm A').toDate() : undefined,
  );
  const [salaryAmount, setSalaryAmount] = useState<any>(
    jobID ? Number(jobID.salaryAmount) : '',
  );
  const [totalSalery, setTotalSalery] = useState<string>(
    jobID ? String(jobID.totalSalaryAmount) : '',
  );
  const [selectedTags, setSelectedTags] = useState<any>(jobID?.skills || []);
  const isDisablePrice = !startDate && !endDate && !startTime && !endTime;

  const [state, setState] = useState({
    settings: {employer: []},
    flatRate: !isEmpty(jobID) ? jobID?.isFlatRate : true,
    customOffer: !isEmpty(jobID) ? jobID?.isPriceOptional : true,
  });

  const {settings, flatRate, customOffer} = state;
  const [duration, setDuration] = useState<string>(
    jobID?.duration
      ? jobID.duration === 'perDay'
        ? 'Per day'
        : jobID.duration === 'perMonth'
        ? 'Per month'
        : jobID.duration === 'perHour'
        ? 'Per hour'
        : jobID.duration === 'perWeek'
        ? 'Per week'
        : ''
      : '',
  );
  const [currency, setCuurency] = useState<string>(
    jobID ? `${jobID.currency}` : 'USD',
  );

  const [amountLoader, setAmountLoader] = useState(false);
  const [durationCal, setDurationCal] = useState('');
  const [location, setLocation] = useState<any>({
    description: jobID?.location || '',
    lat: jobID?.coordinates?.coordinates[1] || null,
    lng: jobID?.coordinates?.coordinates[0] || null,
  });

  const [serviceCharge, setServiceCharge] = useState<string>(
    jobID?.serviceCharge || '',
  ); // Step 2: Define service charge state
  const [totalWithServiceCharge, setTotalWithServiceCharge] = useState<string>(
    jobID?.totalEstimateCharge || '',
  ); // Total with Service Charge

  // Assuming formattedStartDate is an ISO string, convert it to a Date object
  const formattedStartDatecheck: any = startDate;
  const minDate = new Date(formattedStartDatecheck); // Convert to Date object

  // Add a new state to track which image is being uploaded or deleted
  const [uploadingImageIndex, setUploadingImageIndex] = useState<number | null>(
    null,
  );
  const [deletingImageIndex, setDeletingImageIndex] = useState<number | null>(
    null,
  );

  const isDateRequired = !customOffer || salaryAmount;

  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleScroll = () => {
    Keyboard.dismiss();
  };

  const options = [
    <TouchableOpacity
      onPress={() => selectFile()}
      style={[styles.optionsContainer, {paddingVertical: 10, marginLeft: 6}]}>
      <Icon
        name="file-1"
        size={20}
        color={BaseColors.primary}
        style={{paddingRight: 20}}
      />
      <Text
        style={{
          // marginLeft: 15,
          color: BaseColors.primary,
        }}>
        {translate('Files', '')}
      </Text>
    </TouchableOpacity>,
    <TouchableOpacity
      onPress={() => openCamera()}
      style={[styles.optionsContainer, {paddingVertical: 10, marginLeft: 6}]}>
      <Icon
        name="camera"
        size={18}
        color={BaseColors.primary}
        style={{paddingLeft: 9}}
      />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Camera', '')}
      </Text>
    </TouchableOpacity>,

    <TouchableOpacity
      onPress={() => handleChooseFiles()}
      style={[styles.optionsContainer, {marginTop: IOS ? 15 : 0}]}>
      <FIcon
        name="photo"
        size={18}
        color={BaseColors.primary}
        style={{paddingLeft: 9}}
      />
      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Photos', '')}
      </Text>
    </TouchableOpacity>,

    <TouchableOpacity
      onPress={() => {
        if (IOS) {
          ActionSheetRefIOS.current.close();
        } else {
          ActionSheetRef.current.hide();
        }
      }}
      style={[
        styles.optionsContainer,
        {
          paddingVertical: 10,
          marginHorizontal: IOS ? 0 : 20,
          borderTopWidth: IOS ? 3 : 0,
          borderTopColor: BaseColors.textInput,
        },
      ]}>
      <EIcon name="cross" size={18} color={BaseColors.primary} />

      <Text style={{marginLeft: 15, color: BaseColors.primary}}>
        {translate('Cancel', '')}
      </Text>
    </TouchableOpacity>,
  ];

  const CANCEL_INDEX = 3;
  const DESTRUCTIVE_INDEX = 0;
  const ActionSheetRef = useRef<any>();
  const ActionSheetRefIOS = useRef<any>();

  function showActionSheet() {
    if (IOS) {
      ActionSheetRefIOS.current.open();
    } else {
      ActionSheetRef.current.show();
    }
  }

  function doAction(index: number) {
    if (index === 0) {
      selectFile(); // Opens File Picker
    } else if (index === 1) {
      openCamera(); // Opens Camera
    } else if (index === 2) {
      handleChooseFiles(); // Opens Gallery
    }
  }

  useEffect(() => {
    const fetchSettings = async () => {
      const resp = await getSettings();
      console.log('settings?.employer ==>', resp?.data);

      setState(p => ({...p, settings: resp?.data}));
    };

    fetchSettings();
  }, []);

  // UseEffect to calculate total salary when relevant states change
  // useEffect(() => {
  //   if (startDate && endDate && salaryAmount && duration) {
  // const calculatedSalary = calculateTotalSalary(
  //   startDate,
  //   endDate,
  //   startTime,
  //   endTime,
  //   salaryAmount,
  //   duration,
  // );

  // setTotalSalery(calculatedSalary?.toString());
  //   }
  // }, [startDate, endDate, startTime, endTime, salaryAmount, duration]);

  const [selectedImages, setSelectedImages] = useState<any>(
    jobID?.images?.map(img => ({
      fileName: img,
      filePath: img, // Adjust this based on how your image paths are stored
    })) || [],
  );
  console.log('🚀 ~ JobPosting ~ selectedImages:', selectedImages);

  const handleChooseFiles = async () => {
    try {
      if (selectedImages.length >= 5) {
        console.log('Maximum 5 images are allowed.');
        return;
      }

      const results = await ImageCropPicker.openPicker({
        width: 250,
        height: 250,
        cropping: true,
        maxFiles: 5 - selectedImages.length, // Restrict to remaining slots
        multiple: true,
        cropperToolbarTitle: translate('EditPhoto'),
        smartAlbums: smartAlbums,
      });

      if (!results || results.length === 0) {
        return;
      }

      let newImages = results.slice(0, 5 - selectedImages.length); // Ensure max limit

      for (const file of newImages) {
        const tempImage = {filePath: file.path, isUploading: true};
        setSelectedImages((prevFiles: any) => [...prevFiles, tempImage]);

        const filedata: any = {
          uri: file.path,
          type: file.mime,
          name: file.path.split('/').pop(),
        };

        const url = BaseSetting.endpoints.uploadjobFile;
        const imgFile = {file: filedata};

        setImageLoader(true);
        const response = await getApiData({
          endpoint: url,
          method: 'POST',
          data: imgFile,
          formData: true,
        });

        if (response?.status && response?.data) {
          setSelectedImages((prevFiles: any) =>
            prevFiles.map((image: any) =>
              image.filePath === tempImage.filePath
                ? {
                    fileName: response.data.fileName,
                    filePath: response.data.filePath,
                    fileSize: response.data.fileSize,
                    isUploading: false,
                  }
                : image,
            ),
          );
        }
      }

      if (IOS) {
        ActionSheetRefIOS.current.close();
      } else {
        ActionSheetRef.current.hide();
      }
      setImageLoader(false);
    } catch (error) {
      console.error('Error picking images:', error);
    }
  };

  const selectFile = async () => {
    try {
      if (selectedImages.length >= 5) {
        console.log('Maximum 5 files are allowed.');
        return;
      }

      const results = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
        ],
        allowMultiSelection: true, // Allow multiple file selection
      });

      if (!results || results.length === 0) {
        return;
      }

      let newFiles = results.slice(0, 5 - selectedImages.length); // Ensure max limit

      for (const file of newFiles) {
        const tempFile = {filePath: file.uri, isUploading: true};
        setSelectedImages((prevFiles: any) => [...prevFiles, tempFile]);

        const fileData: any = {
          uri: file.uri,
          type: file.type,
          name: file.name,
        };

        const url = BaseSetting.endpoints.uploadjobFile;
        const fileObj = {file: fileData};

        setImageLoader(true);
        const response = await getApiData({
          endpoint: url,
          method: 'POST',
          data: fileObj,
          formData: true,
        });

        if (response?.status && response?.data) {
          setSelectedImages((prevFiles: any) =>
            prevFiles.map((uploadedFile: any) =>
              uploadedFile.filePath === tempFile.filePath
                ? {
                    fileName: response.data.fileName,
                    filePath: response.data.filePath,
                    fileSize: response.data.fileSize,
                    isUploading: false,
                  }
                : uploadedFile,
            ),
          );
        }
      }

      if (IOS) {
        ActionSheetRefIOS.current.close();
      } else {
        ActionSheetRef.current.hide();
      }
      setImageLoader(false);
    } catch (error) {
      console.error('Error picking files:', error);
    }
  };

  const openCamera = async () => {
    try {
      if (selectedImages.length >= 5) {
        console.log('Maximum 5 images are allowed.');
        return;
      }

      const image: any = await ImageCropPicker.openCamera({
        width: 250,
        height: 250,
        cropping: true,
      });

      if (!image) {
        return;
      }

      const tempImage = {filePath: image.path, isUploading: true};
      setSelectedImages((prevFiles: any) => [...prevFiles, tempImage]);

      const filedata: any = {
        uri: image.path,
        type: image.mime,
        name: image.path.split('/').pop(),
      };

      const url = BaseSetting.endpoints.uploadjobFile;
      const imgFile = {file: filedata};

      setImageLoader(true);
      const response = await getApiData({
        endpoint: url,
        method: 'POST',
        data: imgFile,
        formData: true,
      });

      if (response?.status && response?.data) {
        setSelectedImages((prevFiles: any) =>
          prevFiles.map((img: any) =>
            img.filePath === tempImage.filePath
              ? {
                  fileName: response.data.fileName,
                  filePath: response.data.filePath,
                  fileSize: response.data.fileSize,
                  isUploading: false,
                }
              : img,
          ),
        );
      }

      if (IOS) {
        ActionSheetRefIOS.current.close();
      } else {
        ActionSheetRef.current.hide();
      }
      setImageLoader(false);
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };
  const checkAvailable = useCallback(
    async (val: any, type = '') => {
      let d: any = {
        startDate: formatDate(type === 'startDate' ? val : startDate),
        endDate: formatDate(type === 'endDate' ? val : endDate),
        startTime: moment(type === 'startTime' ? val : startTime).format(
          'hh:mm A',
        ),
        endTime: moment(type === 'endTime' ? val : endTime).format('hh:mm A'),
        salaryAmount:
          type === 'salaryAmount'
            ? String(val).replace(/\$/g, '')
            : salaryAmount,
      };

      if (flatRate) {
        d.isFlatRate = flatRate;
        delete d.durationType;
      } else {
        d.durationType = camelCase(trim(duration));
      }

      // Check if all required fields are filled
      const requiredFields = [
        'startDate',
        'endDate',
        'startTime',
        'endTime',
        'salaryAmount',
      ];
      const isMissingFields = requiredFields.some(field => !d[field]);
      console.log('isMissingFields =>', isMissingFields, d);

      if (isMissingFields) {
        setAmountLoader(false);
        return;
      }

      setAmountLoader(true);

      try {
        const url = BaseSetting.endpoints.generateSalary;
        const resp = await getApiData({
          endpoint: url,
          method: 'POST',
          data: d,
        });

        console.log('setServiceCharge ===>', resp);
        if (resp?.status) {
          setServiceCharge(resp?.data?.serviceCharge);
          setTotalSalery(resp?.data?.totalSalaryAmount);
          setTotalWithServiceCharge(resp?.data?.totalEstimateCharge);
        } else {
          // Toast.show(resp?.message || translate('err', ''), Toast.BOTTOM);
        }
      } catch (error) {
        console.error('Error calculating salary:', error);
      } finally {
        setAmountLoader(false);
      }
    },
    [startDate, endDate, startTime, endTime, salaryAmount, duration, flatRate],
  );

  const calculateDur = useCallback(
    (d: any, durType: string, rate?: any) => {
      if (!startDate || !endDate) {
        setDurationCal('');
        return;
      }

      const dur = calculateDuration(
        startDate,
        d || endDate,
        startTime,
        endTime,
        durType || duration,
      );

      const startDateMoment = moment(startDate).startOf('day');
      const endDateMoment = moment(endDate).startOf('day');
      console.log(
        'Invalid dur ===>',
        startDate,
        d || endDate,
        durType || duration,
        endDateMoment.diff(startDateMoment, 'day') + 1,
      );
      if (dur === 'Invalid type') {
        setDurationCal('');
      } else {
        setDurationCal(dur);
      }
    },
    [duration, startDate, endDate, startTime, endTime],
  );

  // Add new useEffect to handle calculations when relevant fields change
  useEffect(() => {
    if (startDate && endDate && startTime && endTime && salaryAmount) {
      if (flatRate) {
        // For flat rate, just calculate service charge
        setTimeout(() => {
          const salaryNumber = parseFloat(salaryAmount);
          if (!isNaN(salaryNumber)) {
            const serviceChargeAmount =
              (salaryNumber * settings?.employer?.chargePercentage) / 100;
            if (!isNaN(salaryNumber + serviceChargeAmount)) {
              setServiceCharge(serviceChargeAmount.toString());
              setTotalWithServiceCharge(
                (salaryNumber + serviceChargeAmount).toString(),
              );
              setTotalSalery(salaryAmount);
            }
          }
        }, 500);
      } else {
        // For non-flat rate, calculate everything
        setTimeout(() => {
          checkAvailable(salaryAmount, 'salaryAmount');
        }, 500);
        // checkAvailable(salaryAmount, 'salaryAmount');
      }
      calculateDur();
    }
  }, [
    startDate,
    endDate,
    startTime,
    endTime,
    salaryAmount,
    duration,
    flatRate,
  ]);

  // Update the start date handler to properly set end date
  const handleStartDateChange = (date: any) => {
    setStartDate(date);
    setSelectDateErrTxt({err: false, txt: ''});
    // If end date is before new start date, update it
    // if (endDate && moment(date).isAfter(moment(endDate))) {
    //   setEndDate(date);
    // }
    if (endDate && moment(date).isSameOrBefore(moment(endDate))) {
      // setEndDate(endDate);
    } else {
      const totalDays = date.diff(date, 'days') + 1;
      // Determine duration type based on total days
      let newDuration = duration; // Keep current duration by default

      if (totalDays >= 365) {
        newDuration = 'Per year';
      } else if (totalDays >= 28) {
        newDuration = 'Per month';
      } else if (totalDays >= 7) {
        newDuration = 'Per week';
      } else if (totalDays >= 2) {
        newDuration = 'Per day';
      } else {
        newDuration = 'Per hour';
      }

      // Only update duration if it's different
      if (newDuration !== duration) {
        setDuration(newDuration);
      }
      setEndDate(date);
      // Recalculate duration and check available
      calculateDur(date, newDuration);
    }
    // checkAvailable(date, 'startDate');
  };

  // Update the end date handler
  const handleEndDateChange = (date: any) => {
    setEndDateErr({err: false, txt: ''});

    // Only calculate duration if we have both start and end dates
    if (startDate) {
      const start = moment(startDate);
      const end = moment(date);
      const totalDays = end.diff(start, 'days') + 1;

      // Determine duration type based on total days
      let newDuration = duration; // Keep current duration by default

      if (totalDays >= 365) {
        newDuration = 'Per year';
      } else if (totalDays >= 28) {
        newDuration = 'Per month';
      } else if (totalDays >= 7) {
        newDuration = 'Per week';
      } else if (totalDays >= 2) {
        newDuration = 'Per day';
      } else {
        newDuration = 'Per hour';
      }

      setEndDate(date);
      // Only update duration if it's different
      if (newDuration !== duration) {
        setDuration(newDuration);
      }

      // Recalculate duration and check available
      calculateDur(date, newDuration);
      // checkAvailable(date, 'endDate');
    }
  };

  const fileNames =
    selectedImages?.map((image: any) => image.fileName) || undefined;

  const handlePost = async (type: string) => {
    if (!userData?.isProfileSet) {
      Toast.show(translate('profileCompletionApproval'), Toast.LONG);
      return false;
    }
    if (type === 'pending') {
      setPostLoader(true);
    } else {
      setLoader(true);
    }

    const formattedStartDate = startDate ? formatDate(startDate) : '';
    const formattedEndDate = endDate ? formatDate(endDate) : '';
    const formattedStartTime = startTime
      ? moment(startTime).format('hh:mm A')
      : undefined;
    const formattedEndTime = endTime
      ? moment(endTime).format('hh:mm A')
      : undefined;
    const currencyCode = currency?.split(' ').pop();

    // Build the newObj with a dynamic description field
    const newObj: any = {
      title: jobName || undefined,
      skills: selectedTags || undefined,
      coordinates: location
        ? {long: location.lng, lat: location.lat}
        : undefined,
      location: location?.description || undefined,
      shortAddress: formatedAddress,
      startDate: formattedStartDate || undefined,
      endDate: formattedEndDate || undefined,
      startTime: formattedStartTime || undefined,
      endTime: formattedEndTime || undefined,
      salaryAmount: salaryAmount || undefined,
      currency: currencyCode || undefined,
      images: fileNames.map((fileName: any) =>
        fileName.includes('http') ? fileName.split('/').pop() : fileName,
      ),
      totalSalaryAmount: Number(totalSalery) || undefined,
      totalEstimateCharge: Number(totalWithServiceCharge) || undefined,
      serviceCharge: serviceCharge || undefined,
      status: type,
      duration:
        duration === 'Per day'
          ? 'perDay'
          : duration === 'Per month'
          ? 'perMonth'
          : duration === 'Per hour'
          ? 'perHour'
          : duration === 'Per week'
          ? 'perWeek'
          : duration === 'Per year'
          ? 'perYear'
          : null,
      isPriceOptional: customOffer,
    };

    // // Add description conditionally only if it's not empty
    if (about) {
      newObj.description = about;
    }

    if (isCustomHarbor) {
      newObj.type = params?.type;
      newObj.seekerId = params?.applicantId;
      newObj.roomId = params?.roomId;
    }

    if (flatRate) {
      newObj.isFlatRate = flatRate;
      if (salaryAmount) {
        newObj.totalSalaryAmount = salaryAmount || 0;
        newObj.serviceCharge =
          (Number(salaryAmount) *
            Number(settings?.employer?.chargePercentage)) /
            100 || 0;
        newObj.totalEstimateCharge =
          Number(salaryAmount) +
          Number(salaryAmount * settings?.employer?.chargePercentage) / 100;
      } else {
        delete newObj.salaryAmount;
        delete newObj.totalSalaryAmount;
        delete newObj.totalEstimateCharge;
        delete newObj.serviceCharge;
      }
      delete newObj.duration;
    }

    try {
      const res = await getApiData({
        endpoint: edit
          ? BaseSetting.endpoints.uploadJobPost + `/${jobID?.id}`
          : BaseSetting.endpoints.jobCreate,
        method: edit ? 'PUT' : 'POST',
        data: newObj,
      });

      console.log('res ===>', res);
      if (res?.status === true) {
        setJobName('');
        setSelectedTags([]);
        setLocation({
          description: '',
          lat: null,
          lng: null,
        });
        setStartDate(undefined);
        setEndDate(undefined);
        setStartTime(undefined);
        setEndTime(undefined);
        setSalaryAmount('');
        setDuration('');
        setCuurency('');
        setAbout('');
        setTotalSalery('');
        setLoader(false);
        setSelectedImages([]);

        if (type === 'edit') {
          getList();
        }
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);

        if (isCustomHarbor) {
          let options = {
            receiver:
              params?.receiverInfo?.userId === userData?.id
                ? params?.receiverInfo?.applicantId
                : userData?.id,
            sender: userData?.id,
            message: 'CUSTOM_HARBOR',
            messageType: 'CUSTOM_HARBOR',
            roomId: params?.roomId,
            senderType: 'Employer',
            jobId: res?.data?.id,
          };
          dispatch(
            emit('send_custom_harbor', options, (res: any) => {
              dispatch(onReceive({data: res?.data}) as any);
            }) as any,
          );
        }

        const slug = 'list_first_job'; // Define the slug
        const {status, badgeInfo} = await updateReview(
          slug,
          userData.id,
          res?.data?.id,
        );

        if (badgeInfo && status) {
          const updatedUserData = {
            ...userData, // Keep all other properties
            badgeInfo, // Update badgeInfo
          };

          dispatch(authActions.setUserData(updatedUserData) as any);
        }
        navigation.goBack();
      } else {
        setLoader(false);
        setPostLoader(false);
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }

      setLoader(false);
      setPostLoader(false);
    } catch (err) {
      console.log('err ===>', JSON.stringify(err));
      setLoader(false);
      setPostLoader(false);
      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  const handlelocationComplete = (val: any, e: any) => {
    const formattedAddress = getFormattedAddress(e);

    setLocation((p: any) => ({
      ...p,
      description: val?.description || '',
      lat: e?.geometry?.location?.lat,
      lng: e?.geometry?.location?.lng,
    }));
    setFormatedAddresss(formattedAddress);

    setLocationErr({err: false, txt: ''});
  };

  //Remove File

  const deleteFile = async (type: string, index?: any) => {
    // Set the deleting index for loading state
    setDeletingImageIndex(index);

    // Extract just the filename from the URL or use the file name as-is
    const fileName = selectedImages[index]?.fileName || selectedImages[index];
    const extractedFileName = fileName.includes('http')
      ? fileName.split('/').pop() // Extract the filename from the URL
      : fileName;

    const data = {
      fileName: extractedFileName, // Pass only the filename
    };

    try {
      const url = BaseSetting.endpoints.removejobFile;

      const resp = await getApiData({
        endpoint: url,
        method: 'POST',
        data: data,
      });

      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);

        // Remove the specific file from selectedImages
        const updatedFiles = selectedImages.filter((_, i) => i !== index);
        setSelectedImages(updatedFiles);
      } else {
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
    } catch (err) {
      // Toast.show(err?.message || 'Something went wrong.', Toast.LONG);
    } finally {
      // Reset deleting index
      setDeletingImageIndex(null);
    }
  };

  const validateFields = (type: any) => {
    let isValid = true;

    // Validate Job Name
    if (!jobName.trim()) {
      setJobNameErr({err: true, txt: translate('jobtitleRequired', '')});
      isValid = false;
    } else if (jobName.length < 3) {
      setJobNameErr({err: true, txt: 'Job Title must have at least 3 words.'});
      isValid = false;
    } else {
      setJobNameErr({err: false, txt: ''});
    }
    if (isEmpty(selectedTags)) {
      setSelectedTagErr({err: true, txt: translate('skillRequired', '')});
      isValid = false;
    } else {
      setSelectedTagErr({err: false, txt: ''});
    }
    if (isDateRequired && !startDate) {
      setSelectDateErrTxt({err: true, txt: translate('startdateRequired', '')});
      isValid = false;
    } else {
      setSelectDateErrTxt({err: false, txt: ''});
    }
    if (isDateRequired && !endDate) {
      setEndDateErr({err: true, txt: translate('enddateRequired', '')});
      isValid = false;
    } else {
      setEndDateErr({err: false, txt: ''});
    }
    if (isDateRequired && !startTime) {
      setStartTimeErr({err: true, txt: translate('starttimeRequired', '')});
      isValid = false;
    } else {
      setStartTimeErr({err: false, txt: ''});
    }
    if (isDateRequired && !endTime) {
      setEndTimeErr({err: true, txt: translate('endtimeRequired', '')});
      isValid = false;
    } else {
      setEndTimeErr({err: false, txt: ''});
    }
    if (
      ((!customOffer && flatRate) || (!flatRate && !customOffer)) &&
      !salaryAmount
    ) {
      setSalaryAmountErr({
        err: true,
        txt: translate(flatRate ? 'totalRequired' : 'rateRequired', ''),
      });
      isValid = false;
    } else {
      setSalaryAmountErr({err: false, txt: ''});
    }
    if (!isDisablePrice && isEmpty(duration) && !flatRate) {
      setDurationErr({err: true, txt: translate('durationRequried', '')});
      isValid = false;
    } else {
      setDurationErr({err: false, txt: ''});
    }
    // if (about?.length > 0 && about?.length < 5) {
    //   setAboutErr({err: true, txt: 'Description is Required'});
    //   isValid = false;
    // } else {
    //   setAboutErr({err: false, txt: ''});
    // }
    if (!location?.description?.trim()) {
      setLocationErr({err: true, txt: translate('joblocationRequired', '')});
      isValid = false;
    } else {
      setLocationErr({err: false, txt: ''});
    }
    // if (!click) {
    //   isValid = false;
    //   setCheckBoxErr(true);
    //   setCheckBoxErrTxt('Please agree to the  our Terms & Conditions');
    // } else {
    //   setCheckBoxErr(false);
    //   setCheckBoxErrTxt('');
    // }

    if (isValid) {
      // if (userData?.isProfileSet === true) {
      handlePost(type);
      // } else {
      //   setModalOpen(p => ({...p, confirmationModal: true}));
      //   // Toast.show(translate('addProfileDetail', ''));
      // }
    }
  };

  const handleDone = (tags: string[]) => {
    setSelectedTags(tags); // Limit selectedTags to 3
    refRBSheet.current?.close();
  };

  return (
    <View style={styles.mainContainer}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="cross"
        leftIconSty={{fontSize: 24}}
        title={translate(isCustomHarbor ? 'customHarbor' : 'jobPost', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <KeyboardAwareScrollView
        onScrollBeginDrag={handleScroll}
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        enableOnAndroid={true}
        extraScrollHeight={100}
        enableResetScrollToCoords={false}>
        <AnimatedView>
          <View style={{marginHorizontal: 15}}>
            <View style={styles.jobPostingView}>
              <Text style={styles.jobDetailText}>
                {translate('jobDetails', '')}
              </Text>
              <View style={styles.paddingTop}>
                <TextInput
                  value={jobName}
                  onChange={(value: any) => {
                    setJobName(value);
                    if (value?.length > 0) {
                      setJobNameErr({err: false, txt: ''});
                    }
                  }}
                  mandatory={true}
                  title={translate('jobTitle', '')}
                  showError={jobNameErr?.err}
                  errorText={jobNameErr?.txt}
                  placeholderText={translate('jobTitle', '')}
                  maxLength={50}
                />
              </View>
              <View>
                <Text style={styles.addSkillsText}>
                  {translate('addSkill', '')}
                  {'*'}
                </Text>
                {/* <AutoComplete
                options={[]}
                placeholder={translate('selecthere', '')}
                // value={}
                // onChange={}
                error={selectedtagErr.err}
                errorText={selectedtagErr.txt}
                setSelectedTags={setSelectedTags}
                selectedTags={selectedTags}
                url={BaseSetting.endpoints.getSkills}

              /> */}
                <SubTagComponant
                  refRBSheet={refRBSheet}
                  setOpenBottomSheet={() => {}}
                  title={translate('selectTag', '')}
                  Description=""
                  cancelProp={() => {}}
                  onClick={handleDone} // Pass the callback
                  doneProp="Done"
                  showDescription={false}
                  deleteSty={false}
                  showCancelbutton={false}
                  options={[]} // Pass your options here
                  setSelectedLanguage={() => {}}
                  selectedLanguage={''}
                  selectedTags={selectedTags} // Pass the actual selectedTags
                  setSelectedTags={setSelectedTags}
                  error={selectedtagErr.err}
                  errorText={selectedtagErr.txt}
                />
              </View>

              <View>
                {/* Job Location */}
                <View style={styles.locationView}>
                  <Placeautocomplete
                    refs={locationRef}
                    isError={locationErr.err}
                    mandatory={true}
                    isErrorMsg={locationErr.txt}
                    onAutoCompleteAddressSelect={(data, details) =>
                      handlelocationComplete(data, details)
                    }
                    placeholder={translate('selectLocation', '')}
                    location={location?.description}
                    isDisable={false}
                    title={translate('selectLocation', '')}
                  />
                </View>
              </View>

              <View style={styles.moreClarificationContainer}>
                <View style={[styles.rowSpaceBetween]}>
                  <Text style={styles.moreClarificationText}>
                    {translate('pay', '')}
                  </Text>
                </View>
                <View style={[styles.row]}>
                  <View style={{paddingTop: 10}}>
                    <SwitchComponent
                      onValueChange={(p: any) => {
                        setState({...state, customOffer: p});
                      }}
                      // disabled={isDisablePrice}
                      value={customOffer}
                    />
                  </View>
                  <Text style={{...styles.flatRate}}>
                    {translate('customOffer')}
                  </Text>
                </View>
                <View style={{...styles.customView, marginTop: 5}}>
                  <Text style={styles.customViewText}>
                    {translate(
                      customOffer ? 'customOfferText' : 'customOffOfferText',
                    )}
                  </Text>
                </View>
                <View style={[styles.row]}>
                  <View
                    style={[
                      styles.rowSpaceBetween,
                      styles.center,
                      {marginTop: 5},
                    ]}>
                    <View style={{paddingTop: 10}}>
                      <SwitchComponent
                        onValueChange={(p: any) => {
                          setState({...state, flatRate: p});
                          if (salaryAmount) {
                            checkAvailable(salaryAmount, 'salaryAmount');
                          }
                        }}
                        // disabled={isDisablePrice}
                        value={flatRate}
                      />
                    </View>
                    <Text style={styles.flatRate}>{translate('flatRate')}</Text>
                  </View>
                </View>
                <View style={{...styles.customView, marginBottom: 10}}>
                  <Text style={styles.customViewText}>
                    {translate('toggleFlat')}
                  </Text>
                </View>
                {flatRate ? (
                  <TextInput
                    value={`$${salaryAmount}`}
                    title={translate('payTotal', '')}
                    keyBoardType="number-pad"
                    onChange={(val: string) => {
                      const numericValue = val.replace(/[^\d.]/g, '');
                      setSalaryAmount(numericValue);
                      calculateDur('', duration, numericValue);
                      checkAvailable(val, 'salaryAmount');
                    }}
                    // iseditable={!isDisablePrice}
                    maxLength={7}
                    style={{backgroundColor: BaseColors.inputBackground}}
                    placeholderText={translate('payTotal', '')}
                    showError={salaryAmountErr.err} // Show error when `errors?.duration` exists
                    errorText={salaryAmountErr.txt} // Display error message
                  />
                ) : (
                  <>
                    <View
                      style={[
                        styles.rowSpaceBetween,
                        {paddingTop: IOS ? 10 : 0},
                      ]}>
                      <View style={styles.inputHalfWidth}>
                        <TextInput
                          value={salaryAmount ? `$${salaryAmount}` : ''}
                          onChange={(val: string) => {
                            // Remove $ sign and any non-numeric characters except decimal point
                            const numericValue = val.replace(/[^\d.]/g, '');
                            setSalaryAmount(numericValue);
                            calculateDur('', duration, numericValue);
                            checkAvailable(val, 'salaryAmount');
                          }}
                          title={translate('rate', '')}
                          keyBoardType="number-pad"
                          maxLength={6} // Increased to account for $ sign
                          placeholderText={`$${translate('addRate', '')}`}
                          showError={salaryAmountErr.err} // Show error when `errors?.duration` exists
                          errorText={salaryAmountErr.txt} // Display error message
                        />
                      </View>
                      <View style={styles.inputHalfWidth}>
                        {/* <TextInput
                      value={duration}
                      title={translate('type', '')}
                      iseditable={false}
                      style={{ backgroundColor: BaseColors.inputBackground }}
                      placeholderText={translate('type', '')}
                    /> */}
                        <DropdownList
                          data={durationOptions}
                          selectedValue={duration}
                          onSelect={selectedValue => {
                            setDuration(selectedValue); // Update the duration state
                            setDurationErr({err: false, txt: ''});
                            checkAvailable(selectedValue, 'durationType');
                          }}
                          viewstyle={{backgroundColor: BaseColors.lightGreyColor, opacity: 0.8}}
                          disable={true}
                          title={translate('type', '')}
                          placeholder={translate('type', '')}
                          showError={durationErr.err} // Show error when `errors?.duration` exists
                          errorTxt={durationErr.txt} // Display error message
                        />
                      </View>
                    </View>
                  </>
                )}
              </View>
              <View style={styles.durationContainer}>
                <View style={styles.rowSpaceBetween}>
                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      Date={true}
                      selectedDate={startDate}
                      onDateChange={handleStartDateChange}
                      mandatory={isDateRequired}
                      title={translate('startDate', '')}
                      minDate={new Date()}
                      datetimemodal="Start Date"
                      showError={selectDateErrTxt.err}
                      errorText={selectDateErrTxt.txt}
                    />
                  </View>
                  <View
                    style={[
                      styles.inputHalfWidth,
                      {paddingBottom: IOS ? 15 : 10},
                    ]}>
                    <TextInput
                      iseditable={startDate ? true : false}
                      Date={true}
                      selectedDate={endDate}
                      onDateChange={handleEndDateChange}
                      mandatory={isDateRequired}
                      title={translate('endDate', '')}
                      minDate={minDate}
                      datetimemodal="End Date"
                      showError={endDateErr.err}
                      errorText={endDateErr.txt}
                    />
                  </View>
                </View>
                <View style={styles.rowSpaceBetween}>
                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      Date={true}
                      selectedDate={startTime}
                      onDateChange={(time: Date) => {
                        setStartTime(time);
                        setStartTimeErr({err: false, txt: ''});
                        // checkAvailable(time, 'startTime');
                      }}
                      title={translate('startTime', '')}
                      dateType="time"
                      Time={true}
                      mandatory={isDateRequired}
                      optionalText={translate('optional')}
                      showError={startTimeErr.err}
                      errorText={startTimeErr.txt}
                      datetimemodal="Start Time"
                    />
                  </View>
                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      Date={true}
                      selectedDate={endTime}
                      onDateChange={(time: Date) => {
                        setEndTime(time);
                        setEndTimeErr({err: false, txt: ''});
                        // checkAvailable(time, 'endTime');
                      }}
                      mandatory={isDateRequired}
                      iseditable={startTime ? true : false}
                      title={translate('endTime', '')}
                      dateType="time"
                      Time={true}
                      showError={endTimeErr.err}
                      errorText={endTimeErr.txt}
                      datetimemodal="End Time"
                    />
                  </View>
                </View>
              </View>
              {!flatRate ? (
                <View
                  style={[
                    styles.rowSpaceBetween,
                    {paddingTop: IOS ? 10 : 10, paddingBottom: 10},
                  ]}>
                  {/* <View style={styles.inputHalfWidth}>
                <DropdownList
                  data={currencyOption || []}
                  selectedValue={currency}
                  onSelect={selectedValue => {
                    setCuurency(selectedValue); // Update the duration state
                    setCurrencyErr({err: false, txt: ''});
                  }}
                  title={translate('currancy', '')}
                  placeholder={translate('Currancy', '')}
                  showError={currencyErr.err} // Show error when `errors?.duration` exists
                  errorTxt={currencyErr.txt} // Display error message
                  containerStyle={{height: null}}
                />
              </View> */}

                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      value={durationCal}
                      title={translate('Duration', '')}
                      iseditable={false}
                      style={{backgroundColor: BaseColors.lightGreyColor}}
                      placeholderText={translate('Duration', '')}
                    />
                  </View>
                  <View style={styles.inputHalfWidth}>
                    <TextInput
                      value={`$${totalSalery}`}
                      editable={false}
                      title={translate('payTotal', '')}
                      keyBoardType="number-pad"
                      iseditable={false}
                      style={{backgroundColor: BaseColors.lightGreyColor}}
                      placeholderText={translate('payTotal', '')}
                    />
                  </View>
                </View>
              ) : null}
              <View style={styles.aboutContainer}>
                <AiComponant
                  about={about}
                  setAbout={setAbout}
                  type="job"
                  selectedTags={selectedTags}
                  jobTitle={jobName}
                  skillsOptions={skillsOptions}
                  setSkillsOptions={setSkillsOptions}
                  location={location}
                  setAiDescription={setAiDescription}
                  aiDescription={aiDescription}
                  startDate={startDate}
                  endDate={endDate}
                  startTime={startTime}
                  endTime={endTime}
                />
              </View>
              <View style={styles.addImageContainer}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <Text style={styles.addImageText}>
                    {translate('addImage', '')}
                  </Text>
                  {/* <Text style={styles.optionalImgTxt}>
                    ({translate('Optional', '')})
                  </Text> */}
                </View>
                <Text style={styles.maxTxtSty}>
                  {translate('maxImage', '')}
                </Text>
                <View style={styles.chooseFilesContainer}>
                  {/* Display selected images */}
                  <View style={styles.imagesRow}>
                    {selectedImages.map((image: any, index: number) => {
                      const isImage = /\.(jpg|jpeg|png|gif)$/i.test(
                        image.fileName,
                      ); // Check if the file is an image

                      return (
                        <View key={index} style={styles.imageWrapper}>
                          {image.isUploading ? (
                            <View
                              style={[
                                styles.uploadedImage,
                                styles.loaderContainer,
                              ]}>
                              <ActivityIndicator
                                size="small"
                                color={BaseColors.primary}
                              />
                            </View>
                          ) : (
                            <>
                              {isImage ? (
                                <FastImage
                                  source={{uri: image.filePath}}
                                  style={styles.uploadedImage}
                                  resizeMode="cover"
                                />
                              ) : (
                                <View style={styles.chooseFilesButton}>
                                  <View
                                    style={{
                                      alignContent: 'center',
                                      justifyContent: 'center',
                                    }}>
                                    <FIcon
                                      name="file"
                                      size={30}
                                      color={BaseColors.primary}
                                      style={{alignSelf: 'center'}}
                                    />
                                  </View>
                                </View>
                              )}
                              <TouchableOpacity
                                style={styles.deleteIcon}
                                onPress={() => {
                                  if (deletingImageIndex !== index) {
                                    deleteFile('licence', index);
                                  }
                                }}
                                disabled={
                                  uploadingImageIndex !== null ||
                                  deletingImageIndex !== null
                                }>
                                {deletingImageIndex === index ? (
                                  <ActivityIndicator color={BaseColors.red} />
                                ) : (
                                  <Text style={styles.deleteText}>X</Text>
                                )}
                              </TouchableOpacity>
                            </>
                          )}
                        </View>
                      );
                    })}

                    {selectedImages.length < 5 && (
                      <View>
                        <TouchableOpacity
                          onPress={showActionSheet}
                          style={styles.chooseFilesButton}
                          disabled={
                            uploadingImageIndex !== null ||
                            deletingImageIndex !== null
                          }>
                          <>
                            <View style={styles.iconViewSty}>
                              <CustomIcon
                                name="Upload"
                                size={20}
                                color={BaseColors.primary}
                              />
                            </View>
                            <Text style={styles.chooseFilesText}>
                              {translate('chooseFiles', '')}
                            </Text>
                          </>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
              </View>
              {!customOffer && flatRate && !salaryAmount ? null : (
                <View style={[styles.jobCostContainer]}>
                  <Text style={styles.jobCostText}>
                    {translate('paymentBreakDown')}{' '}
                  </Text>
                  <Text style={styles.paymentRequiredText}>
                    {translate('paymentRequired', '')}
                  </Text>
                  <View
                    style={[
                      styles.rowSpaceBetween,
                      {paddingTop: IOS ? 15 : 10},
                    ]}>
                    <Text style={styles.totalSalaryText}>
                      {translate('totalSalery', '')}
                    </Text>
                    <Text style={styles.salaryText}>
                      {formatUSD(
                        Number(flatRate ? salaryAmount || 0 : totalSalery || 0),
                      )}
                      {/* ${flatRate ? salaryAmount || 0 : totalSalery || '0'} */}
                    </Text>
                  </View>
                  {settings?.employer?.chargeName ? (
                    <View
                      style={[
                        styles.rowSpaceBetween,
                        {paddingTop: IOS ? 10 : 5},
                      ]}>
                      <Text style={styles.serviceChargeText}>
                        {translate('serviceFee', '')}{' '}
                        {/* {settings?.employer?.chargePercentage > 0
                          ? `(${settings?.employer?.chargePercentage}%)`
                          : null} */}
                      </Text>
                      <Text style={styles.salaryText}>
                        {formatUSD(Number(serviceCharge || 0))}
                        {/* ${serviceCharge || '0'} */}
                      </Text>
                    </View>
                  ) : null}
                  <View style={styles.bottomBorder} />
                  <View
                    style={[
                      styles.rowSpaceBetween,
                      {paddingVertical: IOS ? 14 : 7},
                    ]}>
                    <Text style={styles.estimatedChargesText}>
                      {translate('totalEstimate', '')}
                    </Text>
                    <Text style={styles.salaryAmountText}>
                      {formatUSD(Number(totalWithServiceCharge || 0))}
                      {/* ${totalWithServiceCharge || '0'} */}
                    </Text>
                  </View>
                </View>
              )}
            </View>
          </View>
          <View style={styles.errView}>
            {checkBoxErr && (
              <Text style={styles.checkboxErr}>{checkBoxErrTxt}</Text>
            )}
          </View>
        </AnimatedView>
      </KeyboardAwareScrollView>
      {/* <View style={styles.checkboxView}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setClick(!click);
            setCheckBoxErr(false);
          }}
          style={styles.clickSty}>
          {click ? (
            <CustomIcon
              name="checked"
              style={{color: BaseColors.primary}}
              size={15}
            />
          ) : null}
        </TouchableOpacity>
        <View style={styles.mView}>
          <View style={styles.txtView}>
            <Text style={{color: BaseColors.textColor}}>
              &nbsp; I agree to Harbor's {''}
            </Text>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                navigation.navigate('Policy', {
                  type: 'terms_of_service',
                  title: 'terms and services',
                });
              }}>
              <Text style={styles.txtSty}>terms and services,</Text>
            </TouchableOpacity>
            <Text>{''} </Text>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => navigation.navigate('PrivacyPolicy')}>
              <Text style={styles.txtSty}> privacy policy,</Text>
            </TouchableOpacity>
            <Text> </Text>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                navigation.navigate('Policy', {
                  type: 'cancellation_policy',
                  title: 'cancellation policy',
                });
              }}>
              <Text style={styles.txtSty}>cancellation policy.</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View> */}
      <View style={styles?.btnSty}>
        {edit ? null : (
          <View style={styles?.btnView}>
            <Button
              loading={loader}
              onPress={() => {
                validateFields('draft');
              }}
              disable={amountLoader || imageLoader}
              type="outlined">
              {translate('saveDraft', '')}
            </Button>
          </View>
        )}
        <View style={[styles?.btnView, {width: edit ? '100%' : '47%'}]}>
          <Button
            loading={postLoader}
            onPress={() => {
              validateFields('pending');
            }}
            disable={amountLoader || imageLoader}
            type="text">
            {translate(isCustomHarbor ? 'send' : edit ? 'Update' : 'Post', '')}
          </Button>
        </View>
      </View>
      <ActionSheet
        ref={ActionSheetRef}
        options={options}
        cancelButtonIndex={CANCEL_INDEX}
        destructiveButtonIndex={DESTRUCTIVE_INDEX}
        onPress={(index: any) => doAction(index)}
      />
      <RBSheet
        ref={ActionSheetRefIOS}
        closeOnDragDown={true}
        closeOnPressMask={true}
        dragFromTopOnly={true}
        height={IOS ? 200 : 180}
        customStyles={{
          draggableIcon: {
            width: 50,
            marginTop: 30,
          },
          container: {
            backgroundColor: '#FFF',
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
          },
        }}>
        <View>
          {options?.map(item => {
            return item;
          })}
        </View>
      </RBSheet>
    </View>
  );
}
