import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Keyboard,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import { translate } from '@language/Translate';
import Header from '@components/Header';
import TextInput from '@components/UI/TextInput';
import TabComponent from '@components/TabComponant';
import { BaseColors, BaseStyles } from '@config/theme';
import EmployarCard from '@components/EmployerCard';
import SeekerCard from '@components/SeekerCard';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
import { CustomIcon } from '@config/LoadIcons';
import { isEmpty } from '@app/utils/lodashFactions';
// import Toast from 'react-native-simple-toast';
import SocketActions from '@redux/reducers/socket/actions';
import userConfigActions from '@redux/reducers/userConfig/actions';
import AuthAction from '@redux/reducers/auth/actions';
import { useIsFocused } from '@react-navigation/native';
import {
  formatDate,
  handleAvailabilityUpdate,
  toCamelCase,
} from '@app/utils/CommonFunction';
import ModalComponant from '@components/ModalComponant';
import moment from 'moment';
import MyLoader from '@components/ContentLoader';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import WalkthroughModal from '@components/WalkthroughModal';
import AlertModal from '@components/AlertModal';

const { emit, setTotalMsgCount } = SocketActions;

export default function SearchScreen({ navigation, route }: any) {
  const { useAppSelector } = useRedux();
  const searchItem = route?.params?.searchItem;
  const searchType = route?.params?.searchType;
  const { setSort } = userConfigActions;
  const dispatch = useAppDispatch();
  const { userData } = useAppSelector((state: any) => state.auth);
  const { languageData } = useAppSelector((state: any) => {
    return state.language;
  });
  const [walkthroughVisible, setWalkThrough] = useState<any>(false);
  const { filterData, sort } = useAppSelector((state: any) => state.userConfig); // Use your RootState type
  const { totalMsgCount } = useAppSelector((state: any) => state.socket); // Use your RootState type
  const [loader, setLoader] = useState(false);
  const [saveLoader, setSaveLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);
  const isFiltered = !isEmpty(filterData);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );

  const [list, setList] = useState({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });
  // console.log('list ==>', list);
  const [unReadCount, setUnReadCount] = useState<any>({});

  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });

  const [name, setName] = useState<string>(searchItem ? searchItem : '');

  const [selectedTab, setSelectedTab] = useState<'Seeker' | 'Employer'>(
    searchType || 'Employer',
  );
  const [visible, setVisible] = useState(false);
  const isFocused = useIsFocused();

  useEffect(() => {
    if (!isEmpty(totalMsgCount)) {
      setUnReadCount(totalMsgCount?.data || totalMsgCount);
    } else {
      dispatch(
        emit('unread_message_count', { userId: userData?.id }, (res: any) => {
          console.log('unread_message_count resData ===>', res);
          if (res) {
            setUnReadCount(res);
            dispatch(setTotalMsgCount(res) as any);
          }
        }) as any,
      );
    }
  }, [isFocused, !isEmpty(totalMsgCount)]);

  useEffect(() => {
    console.log('navigation ====>', userData?.isWalkThroughCompleted);
    if (userData?.isWalkThroughCompleted === false) {
      // dispatch(AuthAction.setShowWalkthrough(true) as any);
      setWalkThrough(true);
    }
    return () => { };
  }, []);

  useEffect(() => {
    dispatch(setSort({ label: '', type: '' }) as any);
  }, [selectedTab]);

  // Function to get list of seekers or employers
  const getList = async (
    page = 1,
    bottomLoader: boolean = false,
    query?: string,
    // sort?: any = null,
  ) => {
    // if (loader) return; // Prevent duplicate calls
    if (bottomLoader) {
      setBottomLoading(true);
    } else {
      setLoader(true);
    }
    let data: any = { page, limit: 5 };
    if (selectedTab !== 'Seeker') {
      data = { page, limit: 5, sortKey: 'level', sort: 'ASC' };
    }
    if (selectedTab === 'Employer') {
      data.type = 'home';
    }
    if (query || name?.length >= 3) {
      data.search = query || name;
    }

    if (isFiltered) {
      if (filterData?.skills) {
        data.skillId = filterData?.skills || [];
      }
      if (filterData?.isavailable && selectedTab === 'Seeker') {
        data.isAvailable = filterData?.isavailable || [];
      }
      if (filterData?.verified) {
        data.verified = filterData?.verified || [];
      }
      if (filterData?.flatRate) {
        data.flatRate = filterData?.flatRate || [];
      }
      if (filterData?.customHarbor) {
        data.customHarbor = filterData?.customHarbor || [];
      }
      if (filterData?.duration) {
        data.duration = toCamelCase(filterData?.duration) || '';
      }
      if (filterData?.startDate) {
        data.startDate = formatDate(filterData?.startDate) || '';
      }
      if (filterData?.endDate) {
        data.endDate = formatDate(filterData?.endDate) || '';
      }
      if (filterData?.radiusRange) {
        data.radiusRange = filterData?.radiusRange || '';
      }
      if (filterData?.startTime && filterData?.endTime) {
        data.startTime = moment(filterData?.startTime).isValid()
          ? moment(filterData?.startTime).format('hh:mm A')
          : '';
        data.endTime = moment(filterData?.endTime).isValid()
          ? moment(filterData?.endTime).format('hh:mm A')
          : '';
      }

      if (filterData?.lat) {
        data.lat = filterData?.lat;
      }
      if (filterData?.long) {
        data.long = filterData?.long;
      }
      if (filterData?.location) {
        data.location = filterData?.location;
      }
    }

    if (
      isFiltered &&
      filterData?.salaryStartPoint &&
      filterData?.salaryEndPoint
    ) {
      data.salaryStartPoint = filterData?.salaryStartPoint;
      data.salaryEndPoint = filterData?.salaryEndPoint;
    }

    if (sort?.label && sort?.type) {
      // If the label is "Date listed", change it to "createdAt"
      if (sort.label === 'Date listed') {
        data.sortKey = 'createdAt';
      } else if (sort.label === 'Level') {
        data.sortKey = 'level';
      }
      // If the label is "Pay", change it to "averageRate"
      else if (sort.label === 'Pay') {
        data.sortKey = 'averageRate';
      } else if (selectedTab === 'Seeker') {
        if (sort.label === 'title') {
          data.sortKey = 'title';
        } else if (sort.label === 'Review') {
          data.sortKey = 'averageRate';
        }
      } else if (selectedTab === 'Employer') {
        if (sort.label === 'startDateTime') {
          data.sortKey = 'startDateTime';
        }
      } else {
        // Default to lowercased label for other cases
        data.sortKey = sort.label.toLowerCase();
      }

      // Set the sort order (ASC or DESC)
      data.sort = sort.type === 'Asc' ? 'ASC' : 'DESC';
    }
    try {
      const resp = await getApiData({
        endpoint:
          selectedTab === 'Seeker'
            ? BaseSetting.endpoints.usersList
            : BaseSetting.endpoints.jobList,
        method: selectedTab === 'Seeker' ? 'GET' : 'POST',
        data: data,
      });
      if (resp?.data && resp?.status) {
        setList(p => ({
          ...p,
          data:
            page > 1
              ? [...list?.data, ...resp?.data?.items]
              : resp.data?.items || [],
          pagination: resp?.data?.pagination,
        }));
      } else {
        // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setRefreshing(false);
      setBottomLoading(false);
      setLoader(false);
    } catch (e) {
      setRefreshing(false);
      setBottomLoading(false);
      setLoader(false);
      console.log('ERRR', e);
    }
  };

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      setRefreshing(true);
      getList(1);
      setRefreshing(false);
    }
  }, [loader]);

  const ListEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };
  const handleScroll = (event: any) => {
    // Dismiss keyboard on scroll
    Keyboard.dismiss();

    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      list?.pagination?.isMore &&
      !loader &&
      !bottomLoading
    ) {
      getList(Number(list?.pagination?.currentPage) + 1, true);
    }
  };

  const handleSave = useCallback(
    async (item: any) => {
      setSaveLoader(item?.id);
      try {
        let data: any = {};
        if (selectedTab === 'Seeker') {
          data = { userId: item?.id };
        } else {
          data = { jobId: item?.id };
        }
        const resp = await getApiData({
          endpoint:
            selectedTab === 'Seeker'
              ? BaseSetting.endpoints.seekerSave
              : BaseSetting.endpoints.saveCard,
          method: 'POST',
          data: data,
        });
        if (resp?.status) {
          setList((prevList: any) => ({
            ...prevList,
            data: prevList?.data?.map((i: any) =>
              i.id === item?.id
                ? {
                  ...i,
                  isSaved: resp?.data === 'unsaved' ? false : true, // Revert the save state if error occurs
                  savedUser:
                    resp?.data === 'unsaved' ? null : { id: userData?.id }, // Revert savedUser
                }
                : i,
            ),
          }));
        } else {
          // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
        setSaveLoader(false);
      } catch (e) {
        setSaveLoader(false);
        console.log('ERRR', e);
      }
    },
    [selectedTab],
  );
  const renderItem = (item: any) => {
    if (selectedTab === 'Seeker') {
      return (
        <SeekerCard
          item={item}
          onSave={handleSave}
          saveLoader={saveLoader}
          navigation={navigation}
          type="seeker"
        />
      );
    } else {
      return (
        <EmployarCard
          item={item}
          onSave={handleSave}
          saveLoader={saveLoader}
          navigation={navigation}
          navigateName={'JobApplicant'}
        />
      );
    }
  };

  // Handle user input
  const handleInputChange = (value: string) => {
    setName(value);

    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    if (value.length >= 2) {
      // Set a debounce timeout
      const timeout = setTimeout(() => {
        getList(1, false, value);
      }, 500); // Adjust debounce time as needed (500ms is a common choice)
      setTypingTimeout(timeout);
    } else if (value.length === 0) {
      getList(1);
    }
  };

  useEffect(() => {
    // if (selectedTab === 'Employer') {
    setList(p => ({
      ...p,
      data: [],
      pagination: { currentPage: 1, isMore: null },
    }));
    getList(1, false, name);
    // }
    // return () => {};
  }, [isFiltered, selectedTab, sort, filterData]);

  // useFocusEffect(
  //   React.useCallback(() => {
  //     console.log('useFocuss effect called');
  //     getList(1, false, name);
  // getBatchCount();
  //     return () => {};
  //   }, [isFiltered, selectedTab, sort, filterData]),
  // );

  const hideMenu = () => setVisible(false);

  const getLstData = (sortData?: { label: string; type: string }) => {
    if (sortData) {
      // Update Redux state or directly pass the sort data to getList
      dispatch(setSort(sortData));
    }
  };

  const handleClose = () => {
    setWalkThrough(false);
    // dispatch(AuthAction.setShowWalkthrough(false) as any);
    dispatch(
      AuthAction.setUserData({
        ...userData,
        isWalkThroughCompleted: true,
      }) as any,
    );
    handleAvailabilityUpdate('isWalkThroughCompleted', true);
  };

  return (
    <SafeAreaView style={[styles.container]}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="logo"
        title=""
        onLeftPress={() => {
          navigation.goBack();
        }}
        rightIcons={[
          {
            icon: 'notification1',
            onPress: () => {
              navigation.navigate('Notification');
            },
            notiBadge: true,
            wrapStyle: BaseStyles.notificationIconStyle,
          },
          {
            icon: 'reward-outlined',
            onPress: () => {
              navigation.navigate('RewardScreen');
            },

            // badge: unReadCount?.totalCount || false,
            // wrapStyle: styles.msgIconStyle,
          },
        ]}
      />
      <ModalComponant
        visible={visible}
        hideMenu={hideMenu}
        position={{ right: 25, top: Dimensions.get('screen').height / 3.5 }}
        getLstData={getLstData}
        selectedTab={selectedTab}
      />
      <View style={styles.searchViewSty}>
        <View style={styles.searchFieldSty}>
          <TextInput
            onChange={handleInputChange}
            value={name}
            placeholderText={translate('searchHarbor', '')}
            searchButton={true}
            placeholderStyle={{ width: '82%' }}
          />
        </View>
        <TouchableOpacity
          onPress={() => {
            navigation.navigate('FilterScreen', {
              type: 'home',
              selectedTab: selectedTab,
            });
          }}
          style={{
            ...styles.iconView,
            borderColor: isFiltered
              ? BaseColors.primary
              : BaseColors.bordrColor,
          }}
          activeOpacity={0.5}>
          <CustomIcon
            name="setting"
            size={25}
            color={isFiltered ? BaseColors.primary : BaseColors.inputColor}
            style={{ textAlign: 'center' }}
          />
        </TouchableOpacity>
      </View>
      <ScrollView
        onScroll={handleScroll}
        // scrollEventThrottle={0.5}
        style={{
          ...styles.mainView,
          // marginBottom: Dimensions.get('screen').height / 150,
        }}
        nestedScrollEnabled={true}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}>
        <TabComponent
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          loader={loader}
        />

        <View style={styles.titleContainer}>
          <View>
            <Text style={styles.title}>
              {translate(selectedTab === 'Employer' ? 'featured' : 'featured')}
            </Text>
          </View>
          <View>
            <TouchableOpacity
              onPress={() => {
                // setSort(srt);
                setVisible(true);
                // const srt =
                //   sort === null ? 'ASC' : sort === 'ASC' ? 'DESC' : null;

                // getsort(srt);
              }}
              activeOpacity={0.8}
              style={{
                ...styles.btnStyle,
                borderColor: visible
                  ? BaseColors.primary
                  : BaseColors.bordrColor,
                width: languageData === 'en' ? 80 : 120,
              }}>
              <Text
                style={{
                  ...styles.btnTextStyle,
                  color: visible ? BaseColors.primary : BaseColors.textColor,
                }}>
                {translate('sortBy', '')}
              </Text>
              <CustomIcon
                name="DESC"
                size={15}
                color={visible ? BaseColors.primary : BaseColors.textColor}
              />
            </TouchableOpacity>
          </View>
        </View>
        {loader ? (
          <MyLoader />
        ) : (
          <FlatList
            data={list?.data || []}
            keyExtractor={(item: any) => `${item.id}+1`}
            renderItem={({ item }) => renderItem(item)}
            contentContainerStyle={{
              marginTop: 10,
              marginBottom: Dimensions.get('screen').height / 9,
              marginHorizontal: 10,
            }}
            ListEmptyComponent={
              <View style={styles.centerMain}>
                <NoRecord
                  iconName="employer"
                  title={
                    isFiltered
                      ? 'noMatchFound'
                      : selectedTab === 'Seeker'
                        ? 'noSeeker'
                        : 'noJobs'
                  }
                  description={
                    isFiltered
                      ? 'filterDesc'
                      : selectedTab === 'Seeker'
                        ? 'noSeekerDetail'
                        : 'noJobsDesc'
                  }
                  type={selectedTab}
                />
              </View>
            }
            // onEndReachedThreshold={0.4}
            // onEndReached={({distanceFromEnd}) => {
            //   console.log('distanceFromEnd', distanceFromEnd);
            //   if (distanceFromEnd <= 0) return;
            //   else {
            //     handleEndReached();
            //   }
            // }}
            style={{
              ...styles.mainView,
            }}
            scrollEnabled={false} // Disable FlatList's scrolling
            // refreshControl={
            //   <RefreshControl
            //     refreshing={refreshing}
            //     onRefresh={onRefresh}
            //     colors={[BaseColors.activeTab]} // Customize refresh indicator color
            //     tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
            //   />
            // }
            ListFooterComponent={ListEndLoader} // Loader when loading next page.
          />
        )}
      </ScrollView>
      <WalkthroughModal
        visible={walkthroughVisible}
        onClose={(type?: string) => {
          if (type === 'postJob') {
            if (userData?.isProfileSet === false) {
              setWalkThrough(false);
              setTimeout(() => {
                setModalOpen(p => ({ ...p, confirmationModal: true }));
              }, 100);
            } else {
              navigation?.navigate('JobPosting');
              handleClose();
            }
          } else {
            handleClose();
          }
        }}
      />

      <AlertModal
        image
        title={translate('complete', '')}
        visible={modalOpen.confirmationModal}
        setVisible={(val: any) =>
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
        }
        lottieViewVisible
        btnYPress={() => {
          navigation?.navigate('ProfileSetUp', {});
          handleClose();
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
        }}
        loader={modalOpen?.loader}
        btnYTitle={translate('letsdo')}
        confirmation
        // completeProfile
        titlesty={{ textAlign: 'center' }}
        description={translate('postDescription', '')}
        btnNTitle={translate('maybe')}
        btnNPress={() => {
          handleClose();
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          setTimeout(() => {
            setWalkThrough(true);
          }, 100);
        }}
      />
    </SafeAreaView>
  );
}
