// import Config from 'react-native-config'; // Fetch data from .env
import AsyncStorage from '@react-native-async-storage/async-storage';

// const isLive = Config?.NODE_ENV === 'production';
// const isStaging = Config?.NODE_ENV === 'staging';

// Get the latest environment value from AsyncStorage

// const baseUrl = isLive
//   ? Config?.LIVE_API_URL
//   : isStaging
//     ? Config?.STAGING_API_URL
//     : 'http://192.168.0.148:9000/';

// Default baseUrl and socketUrl
let baseUrl = 'https://api.theharborapp.com';
let socketUrl = 'https://api.theharborapp.com';
// Load persisted baseUrl if available
// This will be executed immediately when the module is loaded
(async () => {
  try {
    const persistedBaseUrl = await AsyncStorage.getItem('HARBOR_BASE_URL');
    if (persistedBaseUrl) {
      baseUrl = persistedBaseUrl;
      socketUrl = persistedBaseUrl;
      console.log('Loaded persisted baseUrl:', persistedBaseUrl);

      // We'll update the BaseSetting object after it's defined
      // See the code after the BaseSetting definition
    }
  } catch (error) {
    console.log('Error loading persisted baseUrl:', error);
  }
})();

// Local and Live IP options (commented out)
// const baseUrl = 'http://************:3000'; // Local
// const baseUrl = 'http://*************:3000'; // Live IP
// const socketUrl = 'http://************:3005'; // Live IP
// const socketUrl = 'http://************:3005'; // Local

const BaseSetting = {
  name: 'harbor',
  displayName: 'harbor',
  appVersionCode: '1',
  baseUrl,
  api: `${baseUrl}`,
  webUrl: 'https://theharborapp.com/',
  socketURL: `${socketUrl}`, // local ip
  sentryApiKey:
    'https://<EMAIL>/4508289272709120',
  googleApiKey: 'AIzaSyDWFlXKe4OeKkCrXyq7WwX39HqxvdYWZoY',
  googleClientId:
    '735633804275-gfarin446qoc0du0bk9c3hm5ianfgfn1.apps.googleusercontent.com',
  // '735633804275-n738f6nk19ggqb904fa6n59li62mucum.apps.googleusercontent.com',
  stripeKey:
    // 'pk_live_51QU5QHP9LGOHYvLhNzI2YkqCEg5gUD5ZWotPqBJKo9JbD77zYj9W6IXH50XyVIgV0hQoGzZzlGrRCmuqvCiiQ4DN00ga15xxWf',
    'pk_test_51QU5QHP9LGOHYvLhdEKYHdFQlQDa8yPwyqapBC7XdhwuOeVjtMrftIo9Cbikf89yZYOmvOKPGczjRIf1R5X2t5Cw00QmJlUW4u',
  // Mohit bhai stripe key
  // 'pk_test_51Qa90EGyePTPEeSy4FDqbgfZSL7lwcig9omPcJfc7vwPuY9RVijSHCSBjDYSMSTYlw8Rel8GCetu0fXB2slhW7bz00DBKK5O6B',
  endpoints: {
    getUser: '/user',
    signin: '/login',
    logout: '/logout',
    signUp: '/signup',
    socialAuth: '/social-login',
    getSkills: '/user-svc/list-skill',
    updateUser: '/user-svc/update-user',
    uploadDocument: '/user-svc/upload-files',
    removeDocument: '/user-svc/remove-files',
    seekerSave: '/user-svc/user/save',

    //job
    jobList: '/job-svc/job-list',
    getFeatured: '/user-svc/get-featured',
    jobCreate: '/job-svc/job',
    currencyList: '/job-svc/list-currencies',
    usersList: '/user-svc/user-list',
    uploadjobFile: '/job-svc/upload-files',
    removejobFile: '/job-svc/remove-files',
    saveCard: '/job-svc/save-userSavedJobs',
    uploadJobPost: '/job-svc/job',
    jobDetail: '/job-svc/job',
    updateuserJob: '/job-svc/update-application',
    locationHistory: '/job-svc/list-search-history',
    listApplicants: '/job-svc/list-job-applicants',
    deleteJob: '/job-svc/job',
    cancelApplication: '/job-svc/cancel-application',
    addRating: 'job-svc/add-rating',
    seekerReview: 'job-svc/review/seeker',
    employerReview: 'job-svc/review/employer',
    contactUs: '/user-svc/contact-us/create',
    faqList: 'user-svc/faq/list',
    generateSalary: '/job-svc/salary-calculation',
    // notification
    getNotification: '/notification-svc/get-notifications',
    readNotification: '/notification-svc/mark-read-notification',
    readAllNotification: '/notification-svc/mark-all-read-notification',
    removeNotification: '/notification-svc/remove-notification',
    batchCount: '/notification-svc/unread-count',
    subSkillList: '/job-svc/list-skill-test',
    // Strip
    getStripIntentId: '/job-svc/create-payment-intent',
    bankDetails: '/user-svc/test-link',
    checkKyc: '/user-svc/verify-stripe-account',
    updateKyc: '/user-svc/update-stripe-account',
    //Terms and Condition
    cmsCondition: 'user-svc/cms/get-by-slug',

    // Reward functionality
    updateReward: 'user-svc/update-reward-point',
    rewardList: 'user-svc/badge-list',

    //Description
    getDescription: '/user-svc/get-job-description',
    transactionHistory: '/user-svc/get-transaction-history',

    getSettings: '/job-svc/get-settings',
    getSystemSettings: 'job-svc/get-system-settings',

    //Add User Data
    AddQualification: '/user-svc/add-user-data',
    deleteQualification: '/user-svc/delete-qualification',
    updateQualification: '/user-svc/update-user-data',

    //Upload is Available
    checkIsAvailable: '/user-svc/update-user-by-key',

    // counter offer
    createOffer: 'job-svc/create-counter-offer',
    createCounterOfferEdit: 'job-svc/counter-offer-edit',
    updateCounterOfferEdit: 'job-svc/counter-offer-update',
    updatePaymentStatus: 'job-svc/update-payment-intent-status',
    cancelPayment: 'job-svc/cancle-payment-intent',
    counterOfferList: 'job-svc/counter-offer-list',
    JobCounterHistory: '/job-svc/counter-offer-details',
    withdrawHistory: '/job-svc/payment/withdraw-history',
    withdrawAmmount: '/user-svc/payment/manual-withdraw',
    getJobDetailsApproval: 'job-svc/get-job-detail-approval',

    //videoTutorial
    videoTutorialList: '/user-svc/tutorials',

    //Report job
    reportJob: '/job-svc/job-report',

    //Delete Account
    deleteAccount:'user-svc/delete-account',
  },
  externalEndpoints: {
    termsService:
      'https://app.termly.io/policy-viewer/policy.html?policyUUID=2c8c4c96-40ed-4941-abba-fa77c9ed1c7d',
    policy:
      'https://app.termly.io/policy-viewer/policy.html?policyUUID=22bf6209-a352-4421-8fc2-b92fc4dbc54e',
  },
};

// Function to update baseUrl from Redux auth state
export const updateBaseUrl = async (newBaseUrl: string) => {
  if (newBaseUrl) {
    baseUrl = newBaseUrl;
    socketUrl = newBaseUrl;

    // Update the BaseSetting object with new values
    BaseSetting.baseUrl = baseUrl;
    BaseSetting.api = `${baseUrl}`;
    BaseSetting.socketURL = `${socketUrl}`;

    // Persist the baseUrl to AsyncStorage
    try {
      await AsyncStorage.setItem('HARBOR_BASE_URL', newBaseUrl);
      console.log('BaseUrl updated and persisted to:', newBaseUrl);
    } catch (error) {
      console.log('Error persisting baseUrl:', error);
    }
  }
};

// Update BaseSetting with persisted values if available
(async () => {
  try {
    const persistedBaseUrl = await AsyncStorage.getItem('HARBOR_BASE_URL');
    if (persistedBaseUrl) {
      // Update the BaseSetting object with persisted values
      BaseSetting.baseUrl = persistedBaseUrl;
      BaseSetting.api = `${persistedBaseUrl}`;
      BaseSetting.socketURL = `${persistedBaseUrl}`;
      console.log('Updated BaseSetting with persisted baseUrl:', persistedBaseUrl);
    }
  } catch (error) {
    console.log('Error updating BaseSetting with persisted baseUrl:', error);
  }
})();

export default BaseSetting;
