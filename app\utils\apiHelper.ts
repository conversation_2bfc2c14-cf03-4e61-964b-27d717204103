import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import * as Sentry from '@sentry/react-native';
import BaseSetting from '../config/setting';
import { store } from '../redux/store/configureStore';
import Toast from 'react-native-simple-toast';
import { isEmpty, isObject } from './lodashFactions';
import { logOutCall } from './CommonFunction';

interface ApiHeaders {
  [key: string]: string;
}

interface ApiDataResponse {
  success?: boolean;
  status?: boolean;
  error?: string;
  message?: string;
  action?: boolean;
  data?: any;
}

interface ErrorResponse {
  status?: any;
  data?: any;
  message?: string;
}

interface ApiRequestOptions {
  endpoint: any;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  headers?: ApiHeaders;
  formData?: boolean;
  onProgress?: (progress: number) => void;
}

export async function getApiData({
  endpoint,
  method,
  data = undefined,
  headers,
  formData = false,
}: // onProgress = () => {}
ApiRequestOptions): Promise<AxiosResponse<ApiDataResponse> | ErrorResponse> {
  const authState = store?.getState() || {};
  const token = authState?.auth?.accessToken || '';
  let authHeaders: ApiHeaders = {
    'Content-Type': 'application/json',
    authorization: token ? `Bearer ${token}` : '',
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  };

  if (headers) {
    authHeaders = headers;
  }
  data = { ...data, platform: 'app' };
  if (formData) {
    authHeaders = {
      'Content-Type': 'multipart/form-data',
      authorization: token ? `Bearer ${token}` : '',
    };
    const formDataObj = new FormData();
    if (data && Object.keys(data).length > 0) {
      Object.keys(data).forEach(key => formDataObj.append(key, data[key]));
    }
    data = formDataObj;
  }

  try {
    const config: AxiosRequestConfig = {
      method,
      url: endpoint,
      baseURL: BaseSetting.api,
      headers: authHeaders,
      // data: !isEmpty(data) ? data : undefined,
    };
    if (method === 'GET') {
      config.params = !isEmpty(data) ? data : undefined;
    } else {
      config.data = !isEmpty(data) ? data : undefined;
    }

    console.log(
      '🚀 ~ config: AxiosRequestConfig.BaseSetting.api + endpoint:',
      JSON.stringify(config),
    );

    const response = await axios(config);

    if (response?.data?.message === 'Unauthorized') {
      await AsyncStorage.setItem('token', '');
    }
    // if (response?.data?.action !== undefined && !response?.data?.action) {
    //   // navigationRef.current?.reset({
    //   //   index: 0,
    //   //   routes: [{ name: 'Auth' }],
    //   // });
    // }
    // if (!response?.data?.status) {
    //   Sentry.captureException(
    //     new Error(response?.data?.message || 'API Error'),
    //     {
    //       extra: {type: 'Api Error', endpoint, authHeaders, data},
    //     },
    //   );
    // }
    if (response?.data?.isUnauthorized === false && !String(endpoint).includes('logout')) {
      await AsyncStorage.setItem('token', '');
      logOutCall();
    }

    if (response?.data?.status === false) {
      // Toast.show(response?.data?.message || "" , Toast.LONG);
    }

    return response?.data;
  } catch (error: any) {
    // Toast.show(error?.message || 'Something went wrong in server, Please try again later.', Toast.LONG);
    console.log('Api error: ===>', error, JSON.stringify(error));
    // Sentry.captureException(new Error(error || 'API Error'),  { endpoint, authHeaders, data });
    if (error?.response) {
      return {
        status: error?.response?.status,
        data: error?.response?.data,
      };
    }
    return error;
    // return {
    //   data: { message: 'Network error' },
    // };
  }
}

interface ProgressRequestOptions {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data: any;
  onProgress: (progress: number) => void;
  customUrl?: string;
}

export function getApiDataProgress({
  endpoint,
  method,
  data,
  onProgress,
  customUrl = '',
}: ProgressRequestOptions): Promise<any> {
  const authState = store?.getState() || {};
  const accessToken = authState?.auth?.accessToken || '';

  const headers: ApiHeaders = {
    'Content-Type': 'multipart/form-data',
    authorization: accessToken ? `Bearer ${accessToken}` : '',
  };

  return new Promise((resolve, reject) => {
    const url = customUrl || BaseSetting.api + endpoint;
    const xhr = new XMLHttpRequest();
    xhr.upload.addEventListener('progress', event => {
      if (event.lengthComputable) {
        const progress = (event.loaded * 100) / event.total;
        onProgress(progress);
      }
    });

    const formDataObj = new FormData();
    Object.keys(data).forEach(key => formDataObj.append(key, data[key]));

    xhr.open(method, url, true);
    xhr.setRequestHeader('Content-Type', 'multipart/form-data');
    if (isObject(headers)) {
      Object.keys(headers).forEach(headerKey => {
        xhr.setRequestHeader(headerKey, headers[headerKey]);
      });
    }

    xhr.send(formDataObj);
    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        try {
          const responseJson = JSON.parse(xhr.responseText);
          if (responseJson?.message !== 'Unauthenticated.') {
            resolve(responseJson);
          }
        } catch (error) {
          reject(error);
        }
      }
    };
  });
}
