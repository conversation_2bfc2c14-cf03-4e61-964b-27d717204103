import Header from '@components/Header';
import Button from '@components/UI/Button';
import TextInput from '@components/UI/TextInput';
import { CustomIcon } from '@config/LoadIcons';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { translate } from '@language/Translate';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Toast from 'react-native-simple-toast';
import socketAction from '@redux/reducers/socket/actions';
import {
  ActivityIndicator,
  FlatList,
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import {
  cloneDeep,
  isArray,
  isEmpty,
  isObject,
  isString,
} from '@app/utils/lodashFactions';
import TypingIndicator from '@components/TypingIndicator/Index';
import {
  handleAvailabilityUpdate,
  isAddressLike,
  isEmailLike,
  isIOS,
  isPhoneNumberLike,
  isSocialLink,
  openMap,
  resetStack,
} from '@app/utils/CommonFunction';
import AnimatedView from '@components/AnimatedView';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import CounterOfferCard from '@components/SeekerCard/CounterOfferCard';
import UserConfigActions from '@redux/reducers/userConfig/actions';

const { emit, setIsReadTic, clearRecievedChatData, setUpdateChat } = socketAction;

const { setUpdateJobData } = UserConfigActions;

const MAX_RETRIES = 5; // Maximum number of retries
const RETRY_DELAY = 1000; // Delay between retries in milliseconds

let isTypingInput = false;
let stopTypingTimeout: any;

const ChatScreen = ({ navigation, route }: any) => {
  const { params } = route;
  const dispatch = useAppDispatch();
  const { useAppSelector } = useRedux();
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState('');
  const [screenLoader, setScreenLoader] = useState(true);
  const { userData } = useAppSelector((auth: any) => auth.auth);
  const { typingData, chatData, isReadTic, updateChatData } = useAppSelector(
    (auth: any) => auth.socket,
  );
  console.log('chatData useEffect ===>', userData?.id, chatData, updateChatData);

  const { updateJobData } = useAppSelector((state: any) => state.userConfig);

  const receiverInfo = params?.userInfo || {};
  const isSeeker = receiverInfo?.selectedTab == 'Seeker';
  const [isPage, setIsPage] = useState({
    page: 1,
    nextEnable: 0,
    bottomLoader: false,
  });
  const nextEnableRef = useRef<any>();
  const [isTyping, setIsTyping] = useState(false);

  const handleData = (respData: any, bottomLoader: any) => {
    console.log(
      'respData ===>',
      bottomLoader,
      respData?.page,
      respData?.nextEnable,
    );
    const pagination = respData;
    if (respData?.success) {
      console.log('pagination ===>', pagination?.page, pagination?.nextEnable);
      const messageList =
        !isEmpty(respData?.data) && isArray(respData?.data)
          ? respData?.data
          : [];

      setIsPage({
        ...isPage,
        page: Number(pagination?.page),
        nextEnable: Number(pagination?.nextEnable),
      });
      nextEnableRef.current = Number(pagination?.nextEnable);
      const finalData = bottomLoader
        ? [...messages, ...messageList]
        : messageList;
      setMessages(finalData);

      const options = {
        receiver: userData?.id,
        roomId: receiverInfo?.id,
        sender:
          receiverInfo?.userId === userData?.id
            ? receiverInfo?.applicantId
            : receiverInfo?.userId,
      };
      dispatch(
        emit('read_message', options, (resData: any) => {
          console.log('read_message resData ===>', resData);
        }),
      );
    }

    if (bottomLoader) {
      setIsPage({
        ...isPage,
        page: pagination?.page,
        nextEnable: pagination?.nextEnable,
        bottomLoader: false,
      });
      nextEnableRef.current = pagination?.nextEnable;
    } else {
      setScreenLoader(false);
    }
  };

  useEffect(() => {
    const fI = messages?.findIndex(
      (o: any) => o?.messageType === 'system_unread_message',
    );
    if (fI !== -1) {
      setTimeout(() => {
        const d = [...messages];
        d.splice(fI, 1);
        setMessages(d);
      }, 5000);
    }
  }, [messages]);

  const resetStopTypingTimeout = (time: number = 5000) => {
    const options = {
      receiver:
        receiverInfo?.userId === userData?.id
          ? receiverInfo?.applicantId
          : userData?.id === receiverInfo?.applicantId
            ? receiverInfo?.userId
            : null,
      roomId: receiverInfo?.id,
      sender: userData?.id,
    };
    if (isTypingInput) {
      clearTimeout(stopTypingTimeout);
    }
    stopTypingTimeout = setTimeout(() => {
      isTypingInput = false;
      dispatch(emit('stop_typing', options));
      stopTypingTimeout = undefined;
    }, time);
  };

  const onTyping = (value: any) => {
    const options = {
      receiver:
        receiverInfo?.userId === userData?.id
          ? receiverInfo?.applicantId
          : receiverInfo?.userId,
      roomId: receiverInfo?.id,
      sender: userData?.id,
    };
    if (value) {
      if (isTypingInput === false) {
        isTypingInput = true;
      }
      dispatch(emit('is_typing', options));
      resetStopTypingTimeout(5000);
    }
  };

  const handleList = async (page = 1, bottomLoader = false) => {
    console.log('receiverInfo ===>', receiverInfo);

    const options = {
      receiverId:
        // receiverInfo?.userId === userData?.id
        //   ? receiverInfo?.applicantId
        //   : userData?.id === receiverInfo?.applicantId ? receiverInfo?.userId : null,
        receiverInfo?.userId,
      userId: userData?.id,
      page: page,
      perPage: 20,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      roomId: receiverInfo?.id || '',
    };

    // Emit the event and return the response
    return new Promise((resolve, reject) => {
      dispatch(
        emit('conversation_detail', options, (respData: any) => {
          resolve(respData); // Return response
        }),
      );
    });
  };

  const retryHandleList = async (page = 1, bottomLoader = false) => {
    let attempts = 0; // Track number of attempts

    while (attempts < MAX_RETRIES) {
      attempts++;
      try {
        const data = await handleList(page, bottomLoader);
        if (data?.success) {
          return data; // Return if success is true
        }
      } catch (error) {
        console.error(`Attempt ${attempts} failed:`, error);
      }
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    }

    throw new Error('Max retries reached'); // Throw an error after max retries
  };

  const getMessagesList = useCallback(
    async (page = 1, bottomLoader = false, type = '') => {
      if (bottomLoader) {
        setIsPage({ ...isPage, bottomLoader: true });
      } else {
        setScreenLoader(type !== 'focused');
      }

      try {
        const data = await retryHandleList(page, bottomLoader); // Use retry mechanism

        if (data?.success) {
          handleData(data, bottomLoader); // Process data if successful
        }
      } catch (error) {
        console.error('Failed to fetch messages:', error);
      } finally {
        if (bottomLoader) {
          setIsPage({ ...isPage, bottomLoader: false });
        } else {
          setScreenLoader(false);
        }
      }
    },
    [route, userData, receiverInfo, messages, isPage],
  );

  useFocusEffect(
    useCallback(() => {
      getMessagesList(isPage?.page, false, 'focused');
    }, [params]),
  );

  useEffect(() => {
    console.log('chatData useEffect ===>', userData?.id, chatData);
    if (updateChatData) {
      // fetchMoreData({ page: 1, refresh: false });
      getMessagesList(1, false, 'focused');
      dispatch(setUpdateChat(false));
    }
  }, [updateChatData]);

  useEffect(() => {
    if (updateJobData) {
      getMessagesList(1, false, 'focused');
      dispatch(setUpdateJobData(false));
    }
    return () => { };
  }, [updateJobData]);

  useEffect(() => {
    console.log('chatData useEffect ===>', userData?.id, chatData);
    if (
      chatData?.data &&
      chatData?.data?.messageType === 'update_counter_offer'
    ) {
      // fetchMoreData({ page: 1, refresh: false });
      getMessagesList(1, false, 'focused');
    }
    if (
      chatData?.data?.data?.conversationRoomId === receiverInfo?.id &&
      !screenLoader
    ) {
      messages?.unshift(chatData?.data?.data);
      const options = {
        receiver: userData?.id,
        roomId: receiverInfo?.id,
        sender:
          receiverInfo?.userId === userData?.id
            ? receiverInfo?.applicantId
            : receiverInfo?.userId,
      };
      dispatch(
        emit('read_message', options, (res: any) => {
          console.log('read_message resData ===>', res);
        }),
      );
      dispatch(clearRecievedChatData());
      // setMessages(messages);
    }
  }, [chatData]);

  useEffect(() => {
    if (isReadTic) {
      if (isReadTic?.roomId === receiverInfo?.id) {
        const msgs = [...messages];
        const updatedMsgs = msgs.map((d: any) => ({
          ...d,
          isRead: 0,
        }));
        console.log('msgs ===>', msgs);
        setMessages(updatedMsgs);
        dispatch(setIsReadTic({}));
      }
    }
  }, [isReadTic]);

  const handleAvailable = (val: any) => {
    handleAvailabilityUpdate(
      'isInfoShared', // key
      val, // value
      res => {
        console.log('🚀 ~ handleAvailable ~ res:', res);
      },
      err => {
        Toast.show(err?.message || translate('err', ''), Toast.BOTTOM);
      },
    );
  };

  useEffect(() => {
    if (!isEmpty(typingData)) {
      if (typingData?.data?.roomId === receiverInfo?.id) {
        //   const msgs = [...messages];
        //   if (messages.findIndex(o => o?.type === 'typing') === -1) {
        //     const isTypingObj = {
        //       id: 'isTyping',
        //       type: 'typing',
        //     };
        //     setMessages([isTypingObj, ...msgs]);
        //   }
        // }
        setIsTyping(true);
      } else {
        // const msgs = [...messages];
        // const fI = messages.findIndex(o => o?.type === 'typing');
        // if (fI !== -1) {
        //   msgs.splice(fI, 1);
        //   setMessages(msgs);
      }
    } else {
      setIsTyping(false);
    }
  }, [typingData]);
  // Street address detection (Generic pattern)
  const addressRegex =
    /^(?=.*\d)(?=.*[A-Za-z\u00C0-\u024F\u4E00-\u9FAF\u0600-\u06FF])(?=.*,).{10,}$/;

  const detectPersonalInfo = (message: string, isPaid: boolean) => {
    const isSocial = isEmailLike(message) || isSocialLink(message);

    // const isSocial =
    //   emailRegex.test(message) ||
    //   phoneRegex.test(message) ||
    //   socialRegex.test(message);
    // If NOT paid, block everything
    if (
      !isPaid &&
      (isSocial || isPhoneNumberLike(message) || isAddressLike(message))
    ) {
      Toast.show(
        'Please keep all communications on the Harbor platform to ensure trust and security.',
      );
      handleAvailable(true);
      // return (isSocial ||
      //   addressRegex.test(message)
      // );
      return isSocial || isPhoneNumberLike(message) || isAddressLike(message);
    }

    const lastMessage = messages?.[messages?.length - 1];
    const lastHadNumber = lastMessage ? isPhoneNumberLike(lastMessage) : false;
    const currentHasNumber = isPhoneNumberLike(message);
    // If both previous and current messages have numbers, assume phone number behavior

    const isPhoneEmailLink =
      isEmailLike(message) ||
      isSocialLink(message) ||
      (lastHadNumber && currentHasNumber);

    // If paid and still sends phone/email/social — block
    if (isPaid && isPhoneEmailLink) {
      Toast.show(
        'Please keep all communications on the Harbor platform to ensure trust and security.',
      );
      handleAvailable(true);
      return isPhoneEmailLink;
    }

    // If paid, only block emails, phone numbers, and social media
    return isSocial;

    // if (isPaid && isSocial) {
    //   Toast.show(
    //     'Please keep all communications on the Harbor platform to ensure trust and security.',
    //   );
    //   handleAvailable(true);
    // }
    // // If paid, only block emails, phone numbers, and social media
    // return emailRegex.test(message) || phoneRegex.test(message) || socialRegex.test(message);
  };

  // Function to handle sending a message
  const handleSend = useCallback(() => {
    if (String(message).trim().length <= 0) {
      return false;
    }
    console.log('receiverInfo?.isPayment ===>', receiverInfo?.isPayment);
    if (detectPersonalInfo(message, receiverInfo?.isPayment)) {
      // Toast.show('Sharing personal contact information is not allowed.');
      setMessage('');
      return false;
    }

    const msg = message;

    setMessage('');
    console.log(
      'reciever ===>',
      userData?.id === receiverInfo?.applicantId,
      receiverInfo,
    );
    let options = {
      receiver:
        userData?.id === receiverInfo?.userId
          ? receiverInfo?.applicantId
          : userData?.id === receiverInfo?.applicantId
            ? receiverInfo?.userId
            : null,
      // receiver:
      // user?.id === selectedChat?.userId
      //   ? selectedChat?.applicantId
      //   : user?.id === selectedChat?.applicantId
      //     ? selectedChat?.userId
      //     : null,
      sender: userData?.id,
      message: String(message).trim(),
      messageType: 'text',
      roomId: receiverInfo?.id,
      senderType: receiverInfo?.selectedTab,
    };

    dispatch(
      emit('send_message', options, (resData: any) => {
        console.log('resData for created At ', resData);
        if (resData?.status) {
          const clonedD = cloneDeep(messages);

          let messageObject: any = {
            ...options,
            ...resData?.data,
            id: resData?.data?.id,
            messageType: resData?.data?.type,
          };
          clonedD.unshift(messageObject);
          console.log('Your Message info is after :', clonedD);
          setMessages(clonedD);
          if (isTypingInput) {
            resetStopTypingTimeout(0);
          }
        } else {
          // Toast.show(
          //   isString(resData?.message)
          //     ? resData?.message || 'Error while sending a message'
          //     : 'Error while sending a message',
          // );
          setMessage(msg);
        }
      }),
    );
  }, [message, route, receiverInfo, userData]);

  // navigation.navigate('JobDetailScreen', {
  //   applicant: applicantData,
  //   type,
  //   job,
  // });
  const renderItem = ({ item }: any) => {
    const isOutgoingMsg = item?.sender === userData?.id;
    const isDateMsg = item?.messageType === 'system_date_message';
    const isUnreadMsg = item?.messageType === 'system_unread_message';
    const isCustomHarbor =
      item?.messageType === 'custom_harbor' ||
      item?.messageType === 'counter_offer';
    const jobConfirmView = item?.messageType === 'Job_confirmation';

    const isLocation = isCustomHarbor
      ? false
      : isAddressLike(String(item?.text || item?.message)); //addressRegex.test(item?.text || item?.message);

    return (
      <View
        key={item?.id}
        style={[
          styles.messageContainer,
          isOutgoingMsg
            ? styles.outgoing
            : isDateMsg || isUnreadMsg
              ? styles.system
              : styles.incoming,
          {
            borderWidth: isOutgoingMsg ? 0 : isDateMsg || isUnreadMsg ? 0 : 1,
            borderColor: BaseColors.lightBlack,
            backgroundColor:
              !isOutgoingMsg && !isDateMsg && !isUnreadMsg
                ? BaseColors.white
                : isDateMsg || isUnreadMsg
                  ? ''
                  : BaseColors.primary,
          },
        ]}>
        {(isCustomHarbor || jobConfirmView) &&
          isObject(item?.message) &&
          !isEmpty(item?.message) ? (
          // <EmployarCard
          //   item={{
          //     ...item?.message,
          //     messageType: item?.messageType,
          //     isChatView: true,
          //   }}
          //   onSave={() => {}}
          //   saveLoader={false}
          //   isSeeker={isSeeker}
          //   navigation={navigation}
          //   navigateName={'JobApplicant'}
          // />
            <CounterOfferCard
              item={item?.message}
              type={'applicants'}
              onActionClick={() => { }}
              applicantsLoader={false}
              navigation={navigation}
              jobDetail={{
                ...item?.message,
                messageType: item?.messageType,
                isChatView: true,
              }}
              handleUpdateJob={(data: any) => {
                if (data) {
                  const msgs = [...messages];
                  console.log('data ===>', data, msgs.findIndex((msg) => msg.id === data.id));
                  const index = msgs.findIndex((msg) => msg.id === data.id);
                  if (index !== -1) {
                    msgs[index] = {
                      ...msgs[index],
                      processing: true,
                    };
                    console.log('Updated msgs ===>', msgs);
                    setMessages(msgs);
                  }
                } else {
                  getMessagesList(isPage?.page, false, 'focused');
                  dispatch(setUpdateJobData(false));
                }
              }}
              chatData={item}
              buttons
            />
          ) : (
            <>
              <Text
                onPress={() => {
                  if (isLocation) {
                    openMap(item?.text || item?.message);
                  }
                }}
                style={{
                  ...styles.messageText,
                  color: isOutgoingMsg
                    ? BaseColors.white
                    : isDateMsg
                      ? BaseColors.primary
                      : BaseColors.textBlackChat,
                  fontSize: isDateMsg || isUnreadMsg ? 12 : 15,
                  textDecorationLine: isLocation ? 'underline' : 'normal',
                }}>
                {isString(item?.text || item?.message)
                  ? item?.text || item?.message
                  : 'UNKNOWN'}
              </Text>
              {isDateMsg || isUnreadMsg ? null : (
                <View style={styles.timeArea}>
                  {/* <Text
                  style={{
                    ...styles.timeText,
                    color: isOutgoingMsg
                      ? BaseColors.white
                      : BaseColors.lightGreyTime,
                  }}>
                  {moment(item.createdAt, 'x').isValid()
                    ? moment(item.createdAt, 'x').format('HH:mm')
                    : '-'}
                </Text> */}
                  {isOutgoingMsg ? (
                  // <IonIcons
                  //   name={item?.isRead === 0 ? 'checkmark-done' : 'checkmark-done'}
                  //   size={15}
                  //   color={BaseColors.white}
                  // />
                    item?.isRead === 0 ? (
                      <Text style={styles?.msgTxt}>Read</Text>
                    ) : (
                      <Text style={styles?.msgTxt}>Sent</Text>
                    )
                  ) : null}
                </View>
              )}
            </>
          )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <Header
        leftIcon="back-arrow"
        title={translate('chat', '')}
        onLeftPress={() => {
          if (receiverInfo?.canGoBack === false) {
            resetStack();
          } else {
            navigation.goBack();
          }
        }}
        ContainerSty={{
          backgroundColor: BaseColors.white,
          // borderBottomWidth: 0.19,
        }}
        chatDetail={receiverInfo || {}}
      />

      {/* <KeyboardAvoidingView
        enabled
        style={{ flex: 1 }}
        behavior={isIOS() ? 'padding' : ''}> */}
      {/* {isPage?.bottomLoader ? (
          <ActivityIndicator size={'small'} color={BaseColors.primary} />
        ) : null} */}
      {/* Chat Area */}
      {screenLoader ? (
        <View style={[styles.chatArea, styles.screenLoaderStyle]}>
          <ActivityIndicator size={'large'} color={BaseColors.primary} />
        </View>
      ) : (
        <AnimatedView>
          <FlatList
            keyboardShouldPersistTaps="handled"
            data={messages}
            extraData={messages}
            renderItem={renderItem}
            keyExtractor={(item, index) => String(item.id).toString() + index}
            contentContainerStyle={styles.chatArea}
            inverted
            onEndReachedThreshold={0.01}
            onEndReached={() => {
              if (nextEnableRef?.current === 1) {
                getMessagesList(Number(isPage?.page) + 1, true);
              }
            }}
          />
        </AnimatedView>
      )}

      {isTyping ? <TypingIndicator type={'chatDetail'} /> : null}

      {/* {!isSeeker ? ( */}
      <View style={styles.customButtonArea}>
        <Button
          loading={false}
          onPress={() => {
            if (userData?.isProfileSet === false) {
              Toast.show(translate('addProfileDetail', ''));
              return false;
            } else {

              navigation.navigate('JobPosting', {
                type: 'custom',
                applicantId:
                  userData?.id === receiverInfo?.userId
                    ? receiverInfo?.applicantId
                    : receiverInfo?.userId,
                receiverInfo: receiverInfo,
                roomId: receiverInfo?.id,
              });
            }
          }}
          icon={
            <CustomIcon
              name="Shop-Bag"
              testID="Shop-Bag"
              color={BaseColors.primary}
              style={{ marginRight: 5 }}
              size={20}
            />
          }
          type="secondary">
          {translate('sendCustom')}
        </Button>
      </View>
      {/* ) : null} */}

      {/* Input Area */}
      <KeyboardAvoidingView
        enabled
        // style={{ flex: 1 }}
        behavior={isIOS() ? 'padding' : ''}>
        <View style={styles.inputArea}>
          <View style={styles.inputSubArea}>
            <TextInput
              style={styles.input}
              value={message}
              textArea
              height={40}
              placeholderText="Type a message..."
              onChange={(text: any) => {
                setMessage(text);
                // onTyping(text);
              }}
              onKeyPress={(nativeEvent: any) => {
                console.log(
                  'nativeEvent?.key ==>',
                  nativeEvent?.key,
                  nativeEvent,
                );
                if (nativeEvent?.key !== 'Backspace') {
                  onTyping(message);
                }
              }}
            />
            <TouchableOpacity style={styles.sendButton} onPress={handleSend}>
              <CustomIcon
                name="Plain"
                testID="alert_modal_image"
                color={BaseColors.white}
                size={20}
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
      {/* </KeyboardAvoidingView> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: BaseColors.white },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: BaseColors.borderColor,
  },

  chatArea: { paddingHorizontal: 10, paddingVertical: 15 },
  screenLoaderStyle: {
    // height: Dimensions.get('screen').height / 1.45,
    flex: 1,
    justifyContent: 'center',
  },
  messageContainer: {
    maxWidth: '90%',
    marginVertical: 5,
    padding: 12,
    borderRadius: 20,
    backgroundColor: BaseColors.lightBlack,
  },
  incoming: {
    alignSelf: 'flex-start',
    backgroundColor: BaseColors.white,
    borderBottomLeftRadius: 0,
  },
  system: {
    alignSelf: 'center',
    // backgroundColor: BaseColors.white,
    // borderBottomLeftRadius: 0,
  },
  outgoing: {
    alignSelf: 'flex-end',
    backgroundColor: BaseColors.primary,
    borderBottomRightRadius: 0,
  },
  messageText: {
    fontSize: 15,
    color: BaseColors.textBlackChat,
    fontFamily: FontFamily.OpenSansMedium,
  },
  timeText: {
    fontSize: 12.5,
    color: BaseColors.grey,
    marginTop: 5,
    textAlign: 'right',
    fontFamily: FontFamily.OpenSansRegular,
  },

  customButtonArea: {
    alignItems: 'center',
    justifyContent: 'center',
    // flex: 1,
    padding: 10,
    // marginBottom: 10,
  },
  timeArea: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    textAlignVertical: 'center',
    gap: 5,
    alignContent: 'center',
  },
  inputArea: {
    flexDirection: 'row',
    alignItems: 'center',
    // flex: 1,
    padding: 10,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: BaseColors.borderColor,
  },
  inputSubArea: {
    flex: 1,
    height: 60,
    // width: Dimensions.get('screen').width / 1.2,
    backgroundColor: BaseColors.chatInputBackground,
    borderRadius: 20,
    paddingHorizontal: 15,
    fontSize: 14,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    // flex: 0.5,
    // height: 60,
    width: '90%',
    // // width: Dimensions.get('screen').width / 1.2,
    backgroundColor: 'transparent',
    // borderRadius: 20,
    // paddingHorizontal: 15,
    // fontSize: 14,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  sendButton: {
    backgroundColor: BaseColors.primary,
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  msgTxt: {
    color: BaseColors.white,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 11,
    paddingLeft: 2,
  },
  sendButtonText: { color: 'white', fontSize: 18 },
});

export default ChatScreen;
