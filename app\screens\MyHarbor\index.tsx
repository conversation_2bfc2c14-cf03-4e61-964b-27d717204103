/* eslint-disable react-native/no-inline-styles */
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import TabComponent from '@components/TabComponant';
import { BaseColors, BaseStyles } from '@config/theme';
import EmployarCard from '@components/EmployerCard';
import SeekerCard from '@components/SeekerCard';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
import { isEmpty } from '@app/utils/lodashFactions';
import { getBatchCount, toCamelCase } from '@app/utils/CommonFunction';
import StatusFilter from '@components/StatusFilter';
import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import AnimatedView from '@components/AnimatedView';
import moment from 'moment';
import TextInput from '@components/UI/TextInput';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MyLoader from '@components/ContentLoader';
import { useRedux } from '@components/UseRedux';
import { useSelector } from 'react-redux';

const HEIGHT = Dimensions.get('screen').height;

export default function MyHarbor({
  navigation,
  type = '',
  id = null,
  harborHistoryList,
  setHarborHistoryList,
}: {
  navigation: any;
  type: string;
  id: any;
  harborHistoryList: any;
  setHarborHistoryList: any;
}) {
  const isSavedHistory = type === 'history';
  const isSavedShortHistory = type === 'short-history';
  const isHistoryView = isSavedHistory || isSavedShortHistory;
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );
  const { useAppSelector } = useRedux();
  const isVisible = !isSavedHistory && !isSavedShortHistory;
  const { harborFilterData } = useAppSelector((state: any) => state.userConfig); // Use your RootState type
  const { userData } = useAppSelector((state: any) => state.auth);
  const [loader, setLoader] = useState(false);
  const [saveLoader, setSaveLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);
  const isFiltered = !isEmpty(harborFilterData);
  const [list, setList] = useState({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });
  const [selectedStatus, setSelectedStatus] = useState<'Saved' | 'Pending'>(
    'Pending',
  );
  const [state, setState] = useState<any>();
  const isFocused = useIsFocused();
  const isSavedFilter = selectedStatus === 'Saved';

  const [selectedTab, setSelectedTab] = useState<'Seeker' | 'Employer'>(
    'Employer',
  );
  const [name, setName] = useState<string>('');

  // Function to get list of seekers or employers
  const getList = async (
    page = 1,
    bottomLoader: boolean = false,
    status: string = 'pending',
    query: string = '',
  ) => {
    if (loader) {
      return;
    } // Prevent duplicate calls
    if (bottomLoader) {
      setBottomLoading(true);
    } else {
      setLoader(true);
    }
    let data: any = {
      page,
      limit: !isSavedShortHistory ? 5 : 3,
    };
    if (selectedTab !== 'Seeker') {
      data = {
        page,
        limit: !isSavedShortHistory ? 5 : 3,
        sortKey: 'level',
        sort: 'ASC',
      };
    }
    if (selectedTab === 'Employer' || selectedTab === 'Seeker') {
      data.type = 'myHarbor';
    }

    // Add this condition to skip isFiltered data when both isSavedShortHistory and isSavedHistory are true
    if (!isHistoryView) {
      if (isFiltered) {
        if (harborFilterData?.skills) {
          data.skillId = harborFilterData?.skills || [];
        }
        if (harborFilterData?.isavailable) {
          data.isavailable = harborFilterData?.isavailable || [];
        }
        if (harborFilterData?.verified) {
          data.verified = harborFilterData?.verified || [];
        }
        if (harborFilterData?.flatRate) {
          data.flatRate = harborFilterData?.flatRate || [];
        }
        if (harborFilterData?.customHarbor) {
          data.customHarbor = harborFilterData?.customHarbor || [];
        }
        if (harborFilterData?.duration) {
          data.duration = toCamelCase(harborFilterData?.duration) || '';
        }
        if (harborFilterData?.startDate && harborFilterData?.endDate) {
          data.startDate = moment(harborFilterData?.startDate).isValid()
            ? moment(harborFilterData?.startDate).format('MM/DD/YYYY')
            : '';
          data.endDate = moment(harborFilterData?.endDate).isValid()
            ? moment(harborFilterData?.endDate).format('MM/DD/YYYY')
            : '';
        }

        if (harborFilterData?.startTime && harborFilterData?.endTime) {
          data.startTime = moment(harborFilterData?.startTime).isValid()
            ? moment(harborFilterData?.startTime).format('hh:mm A')
            : '';
          data.endTime = moment(harborFilterData?.endTime).isValid()
            ? moment(harborFilterData?.endTime).format('hh:mm A')
            : '';
        }
        if (harborFilterData?.kmRadius) {
          data.radiusRange = harborFilterData?.kmRadius || '';
        }

        if (harborFilterData?.lat) {
          data.lat = harborFilterData?.lat;
        }
        if (harborFilterData?.long) {
          data.long = harborFilterData?.long;
        }
        if (harborFilterData?.location) {
          data.location = harborFilterData?.location;
        }
      }

      if (
        isFiltered &&
        harborFilterData?.salaryStartPoint &&
        harborFilterData?.salaryEndPoint
      ) {
        console.log('checkdata');
        data.salaryStartPoint = harborFilterData?.salaryStartPoint;
        data.salaryEndPoint = harborFilterData?.salaryEndPoint;
      }
    }

    if (status || isHistoryView) {
      data.status = isHistoryView
        ? 'completed'
        : status
          ? String(status).toLowerCase()
          : 'pending';
    }

    if (query || name?.length >= 3) {
      data.search = query || name;
    }

    if (isHistoryView && id) {
      data.userId = id;
    }

    try {
      const resp = await getApiData({
        endpoint:
          selectedTab === 'Seeker'
            ? BaseSetting.endpoints.usersList
            : BaseSetting.endpoints.jobList,
        method: selectedTab === 'Seeker' ? 'GET' : 'POST',
        data: data,
      });
      console.log('resp ==>', JSON.stringify(resp));
      if (resp?.data && resp?.status) {
        setList(p => ({
          ...p,
          data:
            page > 1
              ? [...list?.data, ...resp?.data?.items]
              : resp.data?.items || [],
          pagination: resp?.data?.pagination,
        }));
        setHarborHistoryList(resp.data?.items || []);
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
      setRefreshing(false);
      setBottomLoading(false);
      setLoader(false);
    } catch (e) {
      setRefreshing(false);
      setBottomLoading(false);
      setLoader(false);
      console.log('ERRR', e);
    }
  };

  const handleInputChange = (value: string) => {
    setName(value);

    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    if (value.length >= 3) {
      // Set a debounce timeout
      const timeout = setTimeout(() => {
        getList(1, false, state?.status, value);
      }, 500); // Adjust debounce time as needed (500ms is a common choice)
      setTypingTimeout(timeout);
    } else if (value.length === 0) {
      getList(1);
    }
  };

  const handleSave = useCallback(
    async (item: any) => {
      setSaveLoader(item?.id);
      try {
        let data: any = {};
        if (selectedTab === 'Seeker') {
          data = { userId: item?.id };
        } else {
          data = { jobId: item?.id };
        }
        const resp = await getApiData({
          endpoint:
            selectedTab === 'Seeker'
              ? BaseSetting.endpoints.seekerSave
              : BaseSetting.endpoints.saveCard,
          method: 'POST',
          data: data,
        });

        if (resp?.status) {
          console.log('check');
          setList((prevList: any) => ({
            ...prevList,
            data: prevList?.data?.map((i: any) =>
              i.id === item?.id
                ? { ...i, isSaved: item?.isSaved ? false : true }
                : i,
            ),
          }));
        } else {
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
        setSaveLoader(false);
      } catch (e) {
        setSaveLoader(false);
        console.log('ERRR', e);
      }
    },
    [selectedTab],
  );

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      setRefreshing(true);
      getList(1, bottomLoading, state?.status);
      setRefreshing(false);
    }
  }, [loader]);

  const ListEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      list?.pagination?.isMore &&
      !loader &&
      !bottomLoading
    ) {
      getList(Number(list?.pagination?.currentPage) + 1, true, state?.status);
    }
  };

  const renderItem = (item: any) => {
    const isEmployee = userData?.id === item?.userId;
    console.log(
      'called ===>',
      (isEmployee ||
        item?.status !== 'completed' ||
        (item?.jobStatus !== 'Declined' && item?.jobStatus !== null)) &&
      item?.type === 'custom' &&
      isEmpty(item?.approvedApplicant),
      item,
    );
    if (selectedTab === 'Seeker') {
      return (
        <AnimatedView>
          <SeekerCard
            item={item}
            onSave={handleSave}
            saveLoader={saveLoader}
            navigation={navigation}
            type="seeker"
          />
        </AnimatedView>
      );
    } else {
      return (
        <AnimatedView>
          <EmployarCard
            item={item}
            onSave={handleSave}
            isSavedHistory={isSavedShortHistory}
            saveLoader={saveLoader}
            navigation={navigation}
            // navigateName={'JobApplicant'}
            navigateName={'JobApplicant'}
          />
        </AnimatedView>
      );
    }
  };
  // const getBatchCount = async () => {
  //   try {
  //     const resp = await getApiData({
  //       endpoint: BaseSetting.endpoints.batchCount,
  //       method: 'GET',
  //     });
  //     if (resp?.status) {
  //       console.log('abs');
  //       setBatchCount(resp?.unreadCount);
  //     } else {
  //       Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
  //     }
  //   } catch (e) {
  //     console.error('Error fetching notifications:', e);
  //   }
  // };

  // useFocusEffect(
  //   React.useCallback(() => {
  //     console.log('Screen is focused, fetching data');
  //     setList({
  //       data: [],
  //       pagination: { currentPage: 1, isMore: null },
  //     });
  //     getBatchCount();
  //     getList(1, false, state?.status); // Fetch fresh data
  //     return () => {
  //       console.log('Screen is unfocused');
  //     };
  //   }, [
  //     isFiltered,
  //     state?.status,
  //     selectedTab,
  //     harborFilterData,
  //     batchCount,
  //     isFocused,
  //   ]), // Dependencies for fresh fetch
  // );
  useFocusEffect(
    React.useCallback(() => {
      getBatchCount();
      return () => { };
    }, []),
  );

  useEffect(() => {
    getList(1, false, state?.status); // Fetch fresh data
  }, [isFocused, isFiltered, state?.status, selectedTab, harborFilterData]);

  const handleStatusChange = (
    status: 'Saved' | 'Pending' | 'Completed' | 'Upcoming',
  ) => {
    setState((p: any) => ({ ...p, status: status }));
    getList(1, false, String(status).toLowerCase());
  };

  // const handleSaveFun = (status: any) => {
  //   console.log("🚀 ~ handleSaveFun ~ status:", status)
  //   setSelectedStatus(status);
  //   if (status) {
  //     handleStatusChange(status);
  //   }
  // };
  const handleSaveFun = () => {
    const newStatus =
      state?.status === 'Saved'
        ? 'Pending'
        : state?.status === ('Pending' || 'Completed' || 'Upcoming')
          ? 'Saved'
          : 'Saved';
    // Ensure that handleStatusChange always gets the correct status
    handleStatusChange(newStatus);
    setSelectedStatus(newStatus);
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: isVisible ? BaseColors.white : 'transparent',
          flex: 1,
          // marginBottom: 10,
          paddingBottom: isVisible ? 20 : 0,
        },
      ]}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      {isVisible ? (
        <Header
          leftIcon="logo"
          titleViewSty={{ width: '100%' }}
          title={translate('myHarbor', '')}
          rightIcons={[
            {
              icon: 'notification1',
              onPress: () => {
                navigation.navigate('Notification');
              },
              notiBadge: true,
              wrapStyle: BaseStyles.notificationIconStyle,
            },
            {
              icon: 'reward-outlined',
              onPress: () => {
                navigation.navigate('RewardScreen');
              },
              // badge: totalMsgCount?.totalCount || false,
              // wrapStyle: styles.msgIconStyle,
            },
          ]}
        />
      ) : null}
      {type === 'short-history' ? null : type === 'history' ? null : (
        <View style={styles.searchSty}>
          <View style={styles.searchFieldSty}>
            <TextInput
              onChange={handleInputChange}
              value={name}
              placeholderText={translate('myharborSearch', '')}
              searchButton={true}
              placeholderStyle={{ width: '82%' }}
            />
          </View>
          <TouchableOpacity
            style={{
              ...styles.iconView,
            }}
            activeOpacity={0.5}
            onPress={() => {
              handleSaveFun('Saved');
            }}>
            <Ionicons
              name={
                state?.status === 'Saved'
                  ? 'bookmark-sharp' // Filled bookmark if saved
                  : 'bookmark-outline' // Outline bookmark if unsaved
              }
              size={25}
              color={
                state?.status === 'Saved'
                  ? BaseColors.primary // Primary color if saved
                  : BaseColors.black // Black color if unsaved
              }
            />
            {/* <CustomIcon
              name="bookmark"
              size={20}
              color={
                state?.status === 'Saved'
                  ? BaseColors.primary
                  : BaseColors.inputColor
              }
              style={{textAlign: 'center'}}
            /> */}
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={{
              ...styles.iconView,
              borderColor: isFiltered
                ? BaseColors.primary
                : BaseColors.inputColor,
            }}
            activeOpacity={0.5}
            onPress={() => {
              navigation.navigate('FilterScreen', {
                type: 'myHarbor',
                selectedTab: selectedTab,
              });
            }}>
            <CustomIcon
              name="setting"
              size={25}
              color={isFiltered ? BaseColors.primary : BaseColors.inputColor}
              style={{textAlign: 'center'}}
            />
          </TouchableOpacity> */}
        </View>
      )}

      <ScrollView
        onScroll={handleScroll}
        // scrollEventThrottle={0.5}
        style={{
          ...styles.mainView,
          // marginBottom: Dimensions.get('screen').height / 150,
          // marginVertical: isVisible ? 10 : 0,
          // marginHorizontal: isVisible ? 10 : 0,
        }}
        scrollEnabled={!loader}
        nestedScrollEnabled={true}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}>
        {isVisible ? (
          <TabComponent
            selectedTab={selectedTab}
            setSelectedTab={setSelectedTab}
            loader={loader}
          />
        ) : null}

        {isVisible ? (
          <View style={styles.searchViewSty}>
            <View style={styles.container}>
              <StatusFilter
                onStatusChange={handleStatusChange}
                selectedTab={selectedTab}
                navigation={navigation}
                isFiltered={isFiltered}
                setSelectedStatus={setSelectedStatus}
                selectedStatus={selectedStatus}
              />
            </View>
          </View>
        ) : null}
        <AnimatedView>
          {loader ? (
            <MyLoader />
          ) : (
            <FlatList
              data={list?.data || []}
              keyExtractor={(item: any) => `${item.id}+1`}
              renderItem={({ item }) => renderItem(item)}
              contentContainerStyle={{
                marginBottom: isVisible ? HEIGHT / 9 : 0,
              }}
              ListEmptyComponent={
                <View
                  style={[
                    styles.centerMain,
                    { marginTop: isVisible ? HEIGHT / 10 : 0 },
                  ]}>
                  <NoRecord
                    title={
                      selectedTab === 'Seeker'
                        ? 'noSeeker'
                        : isHistoryView
                          ? ''
                          : 'noJobs'
                    }
                    description={
                      selectedTab === 'Seeker'
                        ? 'noSeekerDetail'
                        : isHistoryView
                          ? '-'
                          : 'noJobsDesc'
                    }
                    type={selectedTab}
                    iconName="employer"
                    selectScreen={isHistoryView ? 'isHistoryView' : ''}
                  />
                </View>
              }
              style={{
                // ...styles.mainView,
                marginHorizontal: isVisible ? 10 : 0,
                marginVertical: isVisible ? 10 : 0,
              }}
              scrollEnabled={false} // Disable FlatList's scrolling
              ListFooterComponent={ListEndLoader} // Loader when loading next page.
            />
          )}
        </AnimatedView>
      </ScrollView>
    </View>
  );
}
